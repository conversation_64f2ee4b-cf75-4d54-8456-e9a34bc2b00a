:root {
  --dark: #090d17;
  --primary: #1a3f5b;
  --blue-light: #5098b7;
  --blue-lighter: #68acc8;
  --yellow: #efc95b;
  --yellow-dark: #e0b947;
  --white: #ffffff;
  --gray: #f4f4f4;
  --transition-fast: 0.2s ease;
  --transition-smooth: 0.4s ease;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Rubik', sans-serif;
  background-color: var(--gray);
  color: var(--dark);
  line-height: 1.6;
  scroll-behavior: smooth;
}

a {
  transition: color var(--transition-fast);
}

.top-bar {
  background-color: var(--dark);
  color: var(--white);
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar a {
  color: var(--white);
  margin-left: 20px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.top-bar a.signup {
  background-color: var(--yellow);
  color: var(--dark);
  padding: 8px 16px;
  border-radius: 20px;
  margin-left: 10px;
  transition: background var(--transition-fast);
  display: inline-flex;
  align-items: center;
}

.top-bar a.signup:hover {
  background-color: var(--yellow-dark);
}

.top-bar a.support-link:hover {
  background-color: #45a049;
}

header {
  background-color: var(--primary);
  color: var(--white);
  text-align: center;
  padding: 40px 20px 40px; /* Reduzir o padding superior de 60px para 40px */
  position: relative;
  overflow: hidden;
}

.hero-image-container {
  position: relative;
  width: 100%;
  max-width: 320px;
  margin: -10px auto -10px; /* Reduzir o valor negativo da margem inferior de -20px para -10px */
  z-index: 1;
}

header h1 {
  font-size: clamp(2rem, 5vw, 3.2rem);
  margin: 10px 0 10px; /* Aumentar a margem superior de 5px para 10px */
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.sasquat {
  color: var(--yellow);
}

header p {
  font-size: 1.2rem;
  max-width: 650px;
  margin: 0 auto;
  opacity: 0.95;
}

.section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60px 20px;
  max-width: 1200px;
  margin: auto;
  position: relative;
}

.section-blue {
  background-color: var(--primary);
  color: var(--white);
}

.section h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  position: relative;
  color: var(--primary);
}

.section-blue h2 {
  color: var(--yellow);
}

.section h2::after {
  content: '';
  display: block;
  width: 90%;
  height: 4px;
  background-color: var(--yellow);
  margin: 10px auto 0;
  border-radius: 2px;
}

.section p {
  font-size: 1.1rem;
  max-width: 700px;
  margin-bottom: 40px;
  color: inherit;
  opacity: 0.95;
}

.features {
  display: flex;
  gap: 20px;
  padding-bottom: 10px;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  overflow-x: auto;
  width: 100%;
  flex-wrap: nowrap;
}

.feature-card {
  flex: 0 0 320px;
  background: linear-gradient(145deg, var(--yellow), #ffe9a3);
  padding: 50px 20px;
  border-radius: 16px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  scroll-snap-align: start;
  max-width: 320px;
  height: auto;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.section-blue .feature-card {
  background: var(--white);
  color: var(--dark);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 14px 24px rgba(0, 0, 0, 0.12);
}

.feature-card h3 {
  margin-bottom: 10px;
  font-size: 1.5rem;
  color: var(--primary);
}

.feature-card p {
  font-size: 1rem;
  color: var(--dark);
  flex-grow: 1;
}

.feature-card ul {
  list-style: none;
  padding: 0;
  margin: 20px 0 0;
  text-align: left;
}

.feature-card ul li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  font-size: 0.95rem;
}

.feature-card ul li::before {
  content: "✔";
  position: absolute;
  left: 0;
  color: var(--primary);
  font-weight: bold;
}

.cta-button {
  display: block;
  width: fit-content;
  margin: 60px auto 0;
  background-color: var(--yellow);
  color: var(--dark);
  padding: 15px 35px;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
  border-radius: 50px;
  transition: background var(--transition-fast), transform var(--transition-fast);
}

.cta-button:hover {
  background-color: var(--yellow-dark);
  transform: translateY(-2px);
}

.hero-mascot {
  margin: 40px auto 20px;
  max-width: 240px;
  display: block;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15));
}

.hero-logo {
  margin: 0px auto 0px;
  max-width: 320px;
  display: flex;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15));
}

.faq {
  background-color: var(--blue-lighter);
  padding: 60px 20px;
  text-align: center;
  color: var(--dark);
}

.faq h2 {
  margin-bottom: 40px;
  color: var(--primary);
  position: relative;
}

.faq h2::after {
  content: '';
  display: block;
  width: 90px;
  height: 4px;
  background-color: var(--yellow);
  margin: 10px auto 0;
  border-radius: 2px;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.faq-item {
  background-color: var(--white);
  border-radius: 12px;
  padding: 25px;
  text-align: left;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 180px;
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.faq-item-small {
  flex: 1;
}

.faq-item-medium {
  flex: 2;
}

.faq-item-large {
  flex: 3;
}

.faq-item h3 {
  color: var(--primary);
  margin-bottom: 12px;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 8px;
}

.faq-item h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--yellow);
  border-radius: 1.5px;
}

.faq-item p {
  color: var(--dark);
  font-size: 1rem;
  line-height: 1.5;
  flex-grow: 1;
}

/* Adicionar cores diferentes para cada card */
.faq-row:nth-child(1) .faq-item:nth-child(1) {
  border-top: 4px solid var(--primary);
}

.faq-row:nth-child(1) .faq-item:nth-child(2) {
  border-top: 4px solid var(--yellow);
}

.faq-row:nth-child(2) .faq-item:nth-child(1) {
  border-top: 4px solid var(--blue-light);
}

.faq-row:nth-child(2) .faq-item:nth-child(2) {
  border-top: 4px solid var(--primary);
}

.faq-row:nth-child(2) .faq-item:nth-child(3) {
  border-top: 4px solid var(--yellow);
}

.faq-row:nth-child(3) .faq-item:nth-child(1) {
  border-top: 4px solid var(--blue-light);
}

.faq-row:nth-child(3) .faq-item:nth-child(2) {
  border-top: 4px solid var(--primary);
}

@media (max-width: 1024px) {
  .faq-row {
    flex-wrap: wrap;
  }
  
  .faq-item-small, .faq-item-medium, .faq-item-large {
    flex: 1 1 calc(50% - 10px);
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .faq {
    padding: 40px 15px;
  }
  
  .faq-item {
    padding: 20px;
    min-height: 150px;
  }
  
  .faq-item h3 {
    font-size: 1.1rem;
  }
  
  .faq-item p {
    font-size: 0.95rem;
  }
  
  .faq-item-small, .faq-item-medium, .faq-item-large {
    flex: 1 1 100%;
    min-width: 100%;
  }
}

@media (max-width: 480px) {
  .faq {
    padding: 30px 10px;
  }
  
  .faq-item {
    padding: 15px;
  }
  
  .faq-item h3 {
    font-size: 1rem;
  }
  
  .faq-item p {
    font-size: 0.9rem;
  }
}

footer {
  background-color: var(--dark);
  color: var(--white);
  padding: 30px 20px;
  text-align: center;
}

footer p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

@media (min-width: 1024px) {
  .features {
    overflow-x: visible;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
  }
  
  .feature-card {
    flex: 0 0 320px;
    max-width: 320px;
  }
}

@media (max-width: 1023px) {
  .features {
    overflow-x: auto;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  
  .feature-card {
    flex: 0 0 280px;
    max-width: 280px;
  }
}

@media (max-width: 768px) {
  header h1 {
    font-size: 2.2rem;
  }
  header p {
    font-size: 1rem;
    max-width: 90%;
  }
  .section {
    padding: 40px 15px;
  }
  .section h2 {
    font-size: 1.7rem;
  }
  .section p {
    font-size: 1rem;
    max-width: 90%;
    margin-bottom: 30px;
  }
  .feature-card {
    flex: 0 0 280px;
    padding: 40px 15px;
  }
  .cta-button {
    padding: 12px 28px;
    font-size: 0.95rem;
  }
  .hero-mascot {
    max-width: 180px;
    margin: 30px auto 15px;
  }
  .faq {
    padding: 40px 15px;
  }
  .faq-item {
    padding: 20px;
  }
  .faq-item h3 {
    font-size: 1.1rem;
  }
  .faq-item p {
    font-size: 0.95rem;
  }
  .dashboard-content {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    flex: 1;
  }
}

@media (max-width: 480px) {
  header h1 {
    font-size: 1.8rem;
  }
  header p {
    font-size: 0.9rem;
  }
  .section h2 {
    font-size: 1.4rem;
  }
  .feature-card {
    flex: 0 0 260px;
    padding: 35px 10px;
  }
  .cta-button {
    padding: 10px 22px;
    font-size: 0.9rem;
  }
  .hero-mascot {
    max-width: 140px;
    margin: 20px auto 10px;
  }
  .faq {
    padding: 30px 10px;
  }
  .faq-item {
    padding: 15px;
  }
  .faq-item h3 {
    font-size: 1rem;
  }
  .faq-item p {
    font-size: 0.9rem;
  }
}

/* Dashboard Styles */
.dashboard-container {
  background-color: var(--gray);
  min-height: 100vh;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary);
  color: var(--white);
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.8rem;
}

.logout-button {
  background-color: var(--yellow);
  color: var(--dark);
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.logout-button:hover {
  background-color: var(--yellow-dark);
}

.dashboard-content {
  display: flex;
  gap: 20px;
}

.dashboard-sidebar {
  flex: 0 0 300px;
  min-width: 300px;
  max-width: 300px;
  background-color: var(--white);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow-y: auto;
  height: fit-content;
  max-height: calc(100vh - 100px);
}

.dashboard-main {
  flex: 1;
  background-color: var(--white);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.url-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.url-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--gray);
  border-radius: 8px;
  transition: transform var(--transition-fast);
}

.url-item:hover {
  transform: translateY(-2px);
}

.url-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.url-address {
  font-weight: 600;
  color: var(--primary);
}

.url-status {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
  width: fit-content;
}

.url-status.active {
  background-color: #4caf50;
  color: white;
}

.url-status.inactive {
  background-color: #f44336;
  color: white;
}

.status-help {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
    font-style: italic;
  }

.url-frequency {
  font-size: 0.9rem;
  color: var(--primary);
}

.url-seo {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
  width: fit-content;
  background-color: #9c27b0;
  color: white;
  margin-left: 5px;
}

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab-button {
  background-color: var(--white);
  border: none;
  padding: 10px 20px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.tab-button.active {
  background-color: var(--yellow);
  color: var(--dark);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Archives Styles */
.archives-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.archives-filters select {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.9rem;
}

.fetch-button {
  background-color: var(--yellow);
  color: var(--dark);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.fetch-button:hover {
  background-color: var(--yellow-dark);
}

.archives-container {
  margin-top: 20px;
}

.archives-placeholder {
  color: #666;
  font-style: italic;
}

.loading {
  color: var(--primary);
  font-style: italic;
}

.error {
  color: #f44336;
}

.archives-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.archive-item {
  background-color: var(--gray);
  border-radius: 8px;
  padding: 15px;
  transition: transform var(--transition-fast);
}

.archive-item:hover {
  transform: translateY(-2px);
}

.archive-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.archive-url {
  font-weight: 600;
  color: var(--primary);
  word-break: break-all;
}

.archive-status {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-up {
  background-color: #4caf50;
  color: white;
}

.status-down {
  background-color: #f44336;
  color: white;
}

.archive-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

.archive-message {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #333;
}

/* Status Chart Styles */
.status-chart {
  margin-bottom: 20px;
  background-color: var(--white);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container {
  margin-top: 10px;
}

.chart-bar {
  height: 30px;
  background-color: #f0f0f0;
  border-radius: 15px;
  overflow: hidden;
  display: flex;
  margin-bottom: 10px;
}

.chart-segment {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  transition: width 0.5s ease;
}

.chart-segment.success {
  background-color: #4caf50;
}

.chart-segment.failure {
  background-color: #f44336;
}

.chart-legend {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
}

.legend-color.success {
  background-color: #4caf50;
}

.legend-color.failure {
  background-color: #f44336;
}

.url-filter {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.url-filter select {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.9rem;
  flex-grow: 1;
  max-width: 400px;
}

.executions-summary {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .archives-filters {
    flex-direction: column;
  }
  
  .archive-header, .archive-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .archive-status {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .chart-legend {
    flex-direction: column;
    gap: 5px;
  }
  
  .url-filter {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .url-filter select {
    width: 100%;
    max-width: none;
  }
}

/* Tutorial Button Styles */
.tutorial-btn {
  background: linear-gradient(135deg, var(--yellow), var(--yellow-dark));
  color: var(--dark);
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tutorial-btn:hover {
  background: linear-gradient(135deg, var(--yellow-dark), var(--yellow));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tutorial-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tutorial Modal Styles */
.tutorial-step {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.tutorial-step h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.tutorial-step code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: var(--primary);
  font-weight: 600;
}
