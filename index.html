<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Sasquat - Your Website Watchdog | 24/7 Monitoring</title>
  <meta name="description" content="Sasquat monitors your website's health 24/7, checking uptime, SSL certificates, and SEO issues. Get instant alerts when something goes wrong." />
  <link rel="canonical" href="https://winterforest.io/" />
  <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" href="assets/apple-touch-icon.png">
  <meta name="theme-color" content="#efc95b">
  <meta property="og:title" content="Sasquat - Your Website Watchdog">
  <meta property="og:description" content="Monitor your website's health 24/7. Get instant alerts for downtime, SSL issues, and SEO problems.">
  <meta property="og:image" content="https://winterforest.io/assets/sasquat_social.png">
  <meta property="og:url" content="https://winterforest.io/">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>

  <div class="top-bar">
    <a href="mailto:<EMAIL>?subject=Sasquat Support Request" class="support-link" style="background:#4caf50; color:white; padding:8px 16px; border-radius:6px; text-decoration:none; font-size:0.9rem;">📧 Support</a>
    <div style="display: flex; gap: 10px; align-items: center;">
      <a href="#" id="loginBtn">Login</a>
      <a href="#" class="signup" id="signupBtn">Sign Up</a>
    </div>
  </div>

  <header>
    <div class="hero-image-container">
      <img src="assets/Winter_Forest.png" class="hero-logo" alt="Sasquat Mascot">
    </div>
    <h1><span class="sasquat">Sasquat</span> is Watching</h1>
    <p>Your site is safe. Sasquat keeps an eye on it 24/7 and alerts you the moment something goes wrong.</p>
  </header>

  <section class="section">
    <h2>What Sasquat Does</h2>
    <p>He's cute, he's yellow, and he's relentless. Sasquat monitors your website’s health and tells you if anything breaks.</p>

    <div class="features">
      <div class="feature-card">
        <h3>📈 SEO Helper Alert</h3>
        <p>Ensure your website's SEO is optimized and ensure your website will be ranked higher in search engines. Get insights into your website's SEO performance.</p>
      </div>     
      <div class="feature-card">
        <h3>🌐 Uptime Monitoring</h3>
        <p>Get notified when your site goes down — instantly. Sasquat checks it every few minutes, all day long.</p>
      </div>
      <div class="feature-card">
        <h3>🔒 SSL Certification Check</h3>
        <p>Ensure your website's SSL certificate is valid and up-to-date. Get alerts before it expires or if it becomes invalid.</p>
      </div>
      <div class="feature-card">
        <h3>📬 Email & Telegram Alerts</h3>
        <p>No noise, only signals. Get clear, visual email alerts when your service is unavailable or misbehaving.</p>
        <button class="tutorial-btn" id="telegramTutorialBtn">📱 How to setup Telegram alerts</button>
      </div>
      <div class="feature-card">
        <h3>🧠 Smart Retries</h3>
        <p>Sasquat retries before alerting you, so false alarms don’t interrupt your weekend chill.</p>
      </div>
      <div class="feature-card">
        <h3>💡 Easy Setup</h3>
        <p>No tech headaches. Add a URL, set your email, and let Sasquat do the heavy lifting.</p>
      </div>
    </div>

    <img src="assets/sasquat_mascot.png" class="hero-mascot" alt="Sasquat Mascot">
  </section>

  <section class="section">
    <h2>Choose Your Plan</h2>
    <p>Start small or scale up — Sasquat grows with your needs. All plans use our smart credit system for efficient monitoring.</p>
  
    <div class="features">
      <div class="feature-card">
        <h3>🟡 Standard</h3>
        <p><strong>$9.99/month</strong></p>
        <ul>
          <li>Monitor up to 5 URLs</li>
          <li>Includes SSL validation</li>
          <li>SEO validation not included</li>
          <li>5,400 credits/month</li>
          <li>Credit cost per 1,000 = $1.85</li>
          <li>Feel free to customize your check frequencies</li>
        </ul>
      </div>
      <div class="feature-card">
        <h3>🔹 Plus</h3>
        <p><strong>$19.99/month</strong></p>
        <ul>
          <li>Monitor up to 15 URLs</li>
          <li>Includes SSL validation</li>
          <li>Includes SEO validation</li>
          <li>43,200 total credits/month</li>
          <li>Credit cost per 1,000 = $0.46</li>
          <li>Feel free to customize your check frequencies</li>
        </ul>
      </div>
      <div class="feature-card">
        <h3>🔴 Premium</h3>
        <p><strong>$39.99/month</strong></p>
        <ul>
          <li>Monitor up to 30 URLs</li>
          <li>Includes SSL validation</li>
          <li>Includes SEO validation</li>
          <li>648,000 total credits/month</li>
          <li>Credit cost per 1,000 = ~$0.06</li>
          <li>Feel free to customize your check frequencies</li>
        </ul>
      </div>
    </div>
  
    <h3 style="margin-top: 40px;">💳 Extra Credits</h3>
    <p>Need more checks? Buy extra credits any time.</p>
    <div class="features">
      <div class="feature-card">
        <h3>🎯 Small Pack</h3>
        <p><strong>1,000 Credits - $6.56</strong><br>No discount</p>
      </div>
      <div class="feature-card">
        <h3>⚡ Medium Pack</h3>
        <p><strong>5,000 Credits - $30.00</strong><br>~9% off</p>
      </div>
      <div class="feature-card">
        <h3>🚀 Large Pack</h3>
        <p><strong>10,000 Credits - $56.00</strong><br>~15% off</p>
      </div>
      <div class="feature-card">
        <h3>🏢 Enterprise Pack</h3>
        <p><strong>50,000 Credits - $260.00</strong><br>~20% off</p>
      </div>
      <div class="feature-card">
        <h3>🏛 Corporate Pack</h3>
        <p><strong>100,000 Credits - $500.00</strong><br>~24% off</p>
      </div>
    </div>
  
    <button class="cta-button" id="getStartedBtn">Get Started</button>
  </section>

  <section class="faq">
    <h2>Frequently Asked Questions</h2>
    
    <div class="faq-container">
      <div class="faq-row">
        <div class="faq-item faq-item-large">
          <h3>How does Sasquat's monitoring work?</h3>
          <p>Sasquat performs regular HTTP requests to your URLs at your chosen frequency. It checks for successful responses, validates SSL certificates, and can even scan for SEO issues. Each check consumes credits from your plan, and you'll be notified immediately if any problems are detected.</p>
        </div>
        
        <div class="faq-item faq-item-small">
          <h3>What are credits?</h3>
          <p>Credits are Sasquat's monitoring currency. Each URL check consumes 1 credit. Different check frequencies use different amounts of credits per month.</p>
        </div>
      </div>
      
      <div class="faq-row">
        <div class="faq-item faq-item-small">
          <h3>How do I set up monitoring?</h3>
          <p>Sign up, add your website URLs, and select your preferred check frequency. Sasquat will immediately begin monitoring.</p>
        </div>
        
        <div class="faq-item faq-item-medium">
          <h3>What's the difference between plans?</h3>
          <p>All plans include uptime monitoring and SSL validation. The Plus and Premium plans also include SEO validation. Higher plans offer more URLs, more credits, and a lower cost per credit.</p>
        </div>
        
        <div class="faq-item faq-item-small">
          <h3>Can I change check frequency?</h3>
          <p>Yes! You can customize the check frequency for each URL independently from your dashboard.</p>
        </div>
      </div>
      
      <div class="faq-row">
        <div class="faq-item faq-item-medium">
          <h3>How are alerts delivered?</h3>
          <p>Alerts are sent via email and Telegram (if configured). You'll receive immediate notifications when issues are detected, along with detailed information about the problem.</p>
        </div>
        
        <div class="faq-item faq-item-large">
          <h3>What happens if I run out of credits?</h3>
          <p>If you run out of credits, monitoring will pause until your next billing cycle or until you purchase additional credits. We'll send you a notification before your credits run low so you can take action. You can purchase additional credit packs at any time from your dashboard.</p>
        </div>
      </div>
    </div>
  </section>

  <footer>
    <p>&copy; 2025 Sasquat Inc. All rights reserved. | Built with monster love 💛</p>
    <p style="margin-top: 10px;">
      <a href="mailto:<EMAIL>?subject=Sasquat Support Request" style="color: var(--yellow); text-decoration: none; font-weight: 600;">📧 Need help? Contact Support</a>
    </p>
  </footer>
  <div id="signupPopup" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);justify-content:center;align-items:center;z-index:9999;">
    <div style="background:#fff; border-radius:12px; max-width:600px; width:95%; padding:30px; position:relative; box-shadow:0 6px 16px rgba(0,0,0,0.2); max-height:90vh; overflow-y:auto;">
      <button id="closePopup" style="position:absolute;top:15px;right:15px;background:none;border:none;font-size:1.5rem;cursor:pointer;">&times;</button>
      <h2 style="color:#1a3f5b; margin-bottom:20px;">Sign Up</h2>
      <form id="signupForm" style="display:flex; flex-direction:column; gap:15px;">
        <input type="text" id="name" name="name" placeholder="Name" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
        <input type="email" id="loginEmail" name="loginEmail" placeholder="Email (Login)" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
        
        <label style="display:flex; align-items:center; gap:10px; font-size:0.95rem; user-select:none;">
          <input type="checkbox" id="differentContactEmail" name="differentContactEmail">
          Use different email for contact
        </label>
        <small style="color:#666; font-size:0.85rem; margin-top:-5px; display:none;" id="contactHelpText">First contact must be an email. Additional contacts can be emails or Telegram IDs.</small>
        
        <div id="contactEmailContainer" style="display:none;">
          <div id="contactsContainer">
            <div style="display:flex; gap:10px; align-items:center; margin-bottom:10px;">
              <input type="email" id="contactEmail" name="contactEmail" placeholder="Contact Email (required)" style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem; flex:1;" required>
            </div>
          </div>
          <div style="display:flex; gap:10px; align-items:center; margin-top:5px;">
            <button type="button" id="addContactBtn" style="background:#efc95b; border:none; padding:8px 12px; border-radius:6px; cursor:pointer; font-weight:600; font-size:0.9rem;">+ Add Another Contact</button>
            <button type="button" id="telegramHelpBtn" class="tutorial-btn" style="margin-top:0; font-size:0.8rem; padding:6px 12px;">📱 Telegram Setup</button>
          </div>
        </div>
  
        <input type="password" id="password" name="password" placeholder="Password" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
        <select id="plan" name="plan" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
          <option value="" disabled selected>Select your plan</option>
          <option value="standard">Standard</option>
          <option value="plus">Plus</option>
          <option value="premium">Premium</option>
        </select>
  
        <fieldset style="border:1px solid #ccc; border-radius:8px; padding:10px;">
          <legend style="font-weight:600; color:#1a3f5b;">Site URLs</legend>
          <div id="urlsContainer" style="display:flex; flex-direction:column; gap:10px;"></div>          
          <p id="urlErrorMsg" style="color:red; font-size:0.9rem; display:none; margin-top:6px;"></p>
          <button type="button" id="addUrlBtn" style="margin-top:10px; background:#efc95b; border:none; padding:8px 12px; border-radius:6px; cursor:pointer; font-weight:600;">Add URL</button>
        </fieldset>
  
        <button type="submit" style="background:#efc95b; color:#090d17; border:none; padding:12px; border-radius:50px; font-weight:600; cursor:pointer; font-size:1rem;">Register</button>
      </form>
    </div>
  </div>
  <div id="loginPopup" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);justify-content:center;align-items:center;z-index:9999;">
    <div style="background:#fff; border-radius:12px; max-width:400px; width:95%; padding:30px; position:relative; box-shadow:0 6px 16px rgba(0,0,0,0.2);">
      <button id="closeLoginPopup" style="position:absolute;top:15px;right:15px;background:none;border:none;font-size:1.5rem;cursor:pointer;">&times;</button>
      <h2 style="color:#1a3f5b; margin-bottom:20px;">Login</h2>
      <form id="loginForm" style="display:flex; flex-direction:column; gap:15px;">
        <input type="email" id="loginEmailInput" name="email" placeholder="Email" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
        <input type="password" id="loginPasswordInput" name="password" placeholder="Password" required style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem;">
        <button type="submit" style="background:#efc95b; color:#090d17; border:none; padding:12px; border-radius:50px; font-weight:600; cursor:pointer; font-size:1rem;">Login</button>
      </form>
    </div>
  </div>

  <!-- Telegram Tutorial Modal -->
  <div id="telegramTutorialPopup" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);justify-content:center;align-items:center;z-index:9999;">
    <div style="background:#fff; border-radius:12px; max-width:700px; width:95%; padding:30px; position:relative; box-shadow:0 6px 16px rgba(0,0,0,0.2); max-height:90vh; overflow-y:auto;">
      <button id="closeTelegramTutorial" style="position:absolute;top:15px;right:15px;background:none;border:none;font-size:1.5rem;cursor:pointer;">&times;</button>
      <h2 style="color:#1a3f5b; margin-bottom:20px;">📱 How to Setup Telegram Alerts</h2>

      <div style="display:flex; flex-direction:column; gap:20px;">
        <div style="background:#f8f9fa; padding:15px; border-radius:8px; border-left:4px solid #efc95b;">
          <p style="margin:0; font-weight:600; color:#1a3f5b;">Follow these 3 simple steps to receive Sasquat alerts on Telegram:</p>
        </div>

        <div class="tutorial-step">
          <h3 style="color:#1a3f5b; display:flex; align-items:center; gap:10px;">
            <span style="background:#efc95b; color:#090d17; border-radius:50%; width:30px; height:30px; display:flex; align-items:center; justify-content:center; font-weight:bold;">1</span>
            Start a conversation with @SasquatBot
          </h3>
          <p>Open Telegram and search for <strong>@SasquatBot</strong>. Send the command <code>/start</code> to begin.</p>
          <div style="background:#e3f2fd; padding:10px; border-radius:6px; margin:10px 0;">
            <p style="margin:0; font-family:monospace; color:#1565c0;">💬 Message: /start</p>
          </div>
        </div>

        <div class="tutorial-step">
          <h3 style="color:#1a3f5b; display:flex; align-items:center; gap:10px;">
            <span style="background:#efc95b; color:#090d17; border-radius:50%; width:30px; height:30px; display:flex; align-items:center; justify-content:center; font-weight:bold;">2</span>
            Get your Telegram ID
          </h3>
          <p>You need to find your unique Telegram ID. Search for <strong>@userinfobot</strong> and send <code>/start</code>. The bot will reply with your Telegram ID number.</p>
          <div style="background:#e8f5e8; padding:10px; border-radius:6px; margin:10px 0;">
            <p style="margin:0; font-family:monospace; color:#2e7d32;">🤖 Bot response: Your ID: 123456789</p>
          </div>
          <p style="font-size:0.9rem; color:#666;">💡 <strong>Tip:</strong> Your Telegram ID is a unique number that looks like: 123456789</p>
        </div>

        <div class="tutorial-step">
          <h3 style="color:#1a3f5b; display:flex; align-items:center; gap:10px;">
            <span style="background:#efc95b; color:#090d17; border-radius:50%; width:30px; height:30px; display:flex; align-items:center; justify-content:center; font-weight:bold;">3</span>
            Register your Telegram ID
          </h3>
          <p>When signing up for Sasquat or adding contacts in your dashboard, enter your Telegram ID number in the contact field instead of an email address.</p>
          <div style="background:#fff3e0; padding:10px; border-radius:6px; margin:10px 0;">
            <p style="margin:0; color:#ef6c00;">📝 <strong>Example:</strong> Enter "123456789" in the contact field</p>
          </div>
        </div>

        <div style="background:#e8f5e8; padding:15px; border-radius:8px; border-left:4px solid #4caf50; margin-top:20px;">
          <h4 style="margin:0 0 10px 0; color:#2e7d32;">✅ You're all set!</h4>
          <p style="margin:0; color:#2e7d32;">Once configured, you'll receive instant Telegram notifications whenever Sasquat detects issues with your websites.</p>
        </div>

        <div style="background:#ffebee; padding:15px; border-radius:8px; border-left:4px solid #f44336;">
          <h4 style="margin:0 0 10px 0; color:#c62828;">⚠️ Important Notes:</h4>
          <ul style="margin:0; color:#c62828; padding-left:20px;">
            <li>Make sure to start a conversation with @SasquatBot first</li>
            <li>Your Telegram ID is just numbers (no @ symbol)</li>
            <li>You can use both email and Telegram ID as separate contacts</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script src="script.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

</body>
</html>
