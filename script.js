const API_CONFIG = {
  BASE_URL: 'http://localhost:5080/api',
  ENDPOINTS: {
    CREATE_CLIENT: '/Client/CreateClient',
    UPDATE_CLIENT: '/Client/UpdateClient',
    LOGIN: '/Login/login',
    SEARCH_CLIENT: '/Client/SearchClient',
    GET_EXECUTIONS: '/Executions/GetMonitorExecutions',
    ADD_CREDITS: '/stripe/add_credits'
  }
};

async function callApi(endpoint, data, token = null) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  const noAuthEndpoints = [
    API_CONFIG.ENDPOINTS.CREATE_CLIENT,
    API_CONFIG.ENDPOINTS.LOGIN
  ];
  
  if (!noAuthEndpoints.includes(endpoint)) {
    const authToken = token || localStorage.getItem('userToken');
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    } else {
      console.warn(`Calling authenticated endpoint ${endpoint} without token`);
    }
  } else if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      let errorMessage = 'API request failed';
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (parseError) {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error(`API Error (${endpoint}):`, error);

    // Provide more helpful error messages for common issues
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Unable to connect to the server. Please check if the backend service is running.');
    } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
      throw new Error('Network error. Please check your internet connection and try again.');
    }

    throw error;
  }
}

async function callGetApi(endpoint, data, token = null) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  const noAuthEndpoints = [
    API_CONFIG.ENDPOINTS.CREATE_CLIENT,
    API_CONFIG.ENDPOINTS.LOGIN
  ];
  
  if (!noAuthEndpoints.includes(endpoint)) {
    const authToken = token || localStorage.getItem('userToken');
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    } else {
      console.warn(`Calling authenticated endpoint ${endpoint} without token`);
    }
  } else if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  try {
    
    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: headers
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'API request failed');
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API Error (${endpoint}):`, error);
    throw error;
  }
}

async function callPutApi(endpoint, data, token = null) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  const noAuthEndpoints = [
    API_CONFIG.ENDPOINTS.CREATE_CLIENT,
    API_CONFIG.ENDPOINTS.LOGIN
  ];
  
  if (!noAuthEndpoints.includes(endpoint)) {
    const authToken = token || localStorage.getItem('userToken');
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    } else {
      console.warn(`Calling authenticated endpoint ${endpoint} without token`);
    }
  } else if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: headers,
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'API request failed');
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API Error (${endpoint}):`, error);
    throw error;
  }
}

const planMap = {
  standard: 0,
  plus: 1,
  premium: 2
};

const planLimitMap = {
  standard: 5,
  plus: 15,
  premium: 30
};

function getCurrentPlanLimit() {
  const planSelect = document.querySelector('select[name="plan"]');
  const selectedPlan = planSelect?.value?.toLowerCase() || 'standard';
  return planLimitMap[selectedPlan] || 5;
}

function setupUrlInputs() {
  const urlInputs = document.querySelectorAll('input[type="url"]');
  
  urlInputs.forEach(input => {
    input.addEventListener('focus', function() {
      if (!this.value) {
        this.value = 'https://';
      } else if (!this.value.startsWith('http://') && !this.value.startsWith('https://')) {
        this.value = 'https://' + this.value;
      }
    });
    
    input.addEventListener('blur', function() {
      if (this.value && this.value !== 'https://' && 
          !this.value.startsWith('http://') && !this.value.startsWith('https://')) {
        this.value = 'https://' + this.value;
      } else if (this.value === 'https://') {
        this.value = '';
      }
    });
  });
}

function createUrlItem() {
  const urlItem = document.createElement('div');
  urlItem.style.display = 'flex';
  urlItem.style.gap = '10px';
  urlItem.style.alignItems = 'center';

  const urlInput = document.createElement('input');
  urlInput.type = 'url';
  urlInput.name = 'urls[]';
  urlInput.placeholder = 'https://example.com';
  urlInput.required = true;
  urlInput.style.flex = '1';
  urlInput.style.padding = '8px';
  urlInput.style.border = '1px solid #ccc';
  urlInput.style.borderRadius = '6px';
  urlInput.style.fontSize = '1rem';
  
  urlInput.addEventListener('focus', function() {
    if (!this.value) {
      this.value = 'https://';
    } else if (!this.value.startsWith('http://') && !this.value.startsWith('https://')) {
      this.value = 'https://' + this.value;
    }
  });
  
  urlInput.addEventListener('blur', function() {
    if (this.value && this.value !== 'https://' && 
        !this.value.startsWith('http://') && !this.value.startsWith('https://')) {
      this.value = 'https://' + this.value;
    } else if (this.value === 'https://') {
      this.value = '';
    }
  });

  const frequencySelect = document.createElement('select');
  frequencySelect.name = 'frequencies[]';
  frequencySelect.required = true;
  frequencySelect.style.padding = '8px';
  frequencySelect.style.border = '1px solid #ccc';
  frequencySelect.style.borderRadius = '6px';
  frequencySelect.style.fontSize = '1rem';

  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = 'Frequency';
  defaultOption.disabled = true;
  defaultOption.selected = true;
  frequencySelect.appendChild(defaultOption);

  const frequencies = ['Daily', 'Hourly', 'Every 2 Hours', 'Every 6 Hours', 'Every 12 Hours'];
  frequencies.forEach(freq => {
    const option = document.createElement('option');
    option.value = freq;
    option.textContent = freq;
    frequencySelect.appendChild(option);
  });


  const seoCheckboxContainer = document.createElement('div');
  seoCheckboxContainer.className = 'form-group checkbox-group';
  
  const seoCheckboxLabel = document.createElement('label');
  seoCheckboxLabel.className = 'checkbox-label';
  
  const seoCheckbox = document.createElement('input');
  seoCheckbox.type = 'checkbox';
  seoCheckbox.id = 'seoCheck';
  seoCheckbox.name = 'checkSeo';
  seoCheckbox.checked = true;
  
  const seoSpan = document.createElement('span');
  seoSpan.textContent = 'Check SEO';
  
  seoCheckboxLabel.appendChild(seoCheckbox);
  seoCheckboxLabel.appendChild(seoSpan);
  seoCheckboxContainer.appendChild(seoCheckboxLabel);
  

  const planSelect = document.querySelector('select[name="plan"]');
  const selectedPlan = planSelect?.value?.toLowerCase() || 'standard';
  if (selectedPlan === 'standard') {
    seoCheckboxContainer.style.display = 'none';
  }

  const removeBtn = document.createElement('button');
  removeBtn.type = 'button';
  removeBtn.textContent = '×';
  removeBtn.style.background = '#f44336';
  removeBtn.style.color = 'white';
  removeBtn.style.border = 'none';
  removeBtn.style.borderRadius = '50%';
  removeBtn.style.width = '30px';
  removeBtn.style.height = '30px';
  removeBtn.style.fontSize = '1.2rem';
  removeBtn.style.cursor = 'pointer';
  removeBtn.style.display = 'flex';
  removeBtn.style.justifyContent = 'center';
  removeBtn.style.alignItems = 'center';
  removeBtn.style.padding = '0';
  removeBtn.style.lineHeight = '1';

  removeBtn.addEventListener('click', () => {
    urlItem.remove();
    const errorMsg = document.getElementById('urlErrorMsg');
    if (errorMsg) {
      errorMsg.style.display = 'none';
    }
  });

  urlItem.appendChild(urlInput);
  urlItem.appendChild(frequencySelect);
  urlItem.appendChild(seoCheckboxContainer);
  urlItem.appendChild(removeBtn);

  return urlItem;
}

function isTokenValid(token) {
  if (!token) return false;
  
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const payload = JSON.parse(jsonPayload);
    
    if (payload.exp) {
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      
      return currentTime < expirationTime;
    }
    
    return true;
  } catch (e) {
    console.error('Error checking token validity', e);
    return false;
  }
}

function handleAuthError(error) {
  if (error.message && (
      error.message.includes('token') || 
      error.message.includes('auth') || 
      error.message.includes('unauthorized') ||
      error.message.includes('forbidden')
    )) {
    console.warn('Authentication error detected, clearing token');
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    
    const dashboardContainer = document.querySelector('.dashboard-container');
    if (dashboardContainer) {
      document.body.removeChild(dashboardContainer);
      
      document.querySelectorAll('section, header, footer, .top-bar').forEach(el => {
        el.style.display = '';
      });
      
      const loginPopup = document.getElementById('loginPopup');
      if (loginPopup) {
        loginPopup.style.display = 'flex';
        alert('Your session has expired. Please log in again.');
      }
    }
  }
}

function formatNumberWithPeriods(number) {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

function parseJwt(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const payload = JSON.parse(jsonPayload);
    
    return {
      id: payload.nameid,
      name: payload.unique_name,
      email: payload.email
    };
  } catch (e) {
    console.error('Error parsing JWT token', e);
    return {
      name: 'User',
      email: 'Not available'
    };
  }
}

function getPlanName(planNumber) {
  const plans = ['Standard', 'Plus', 'Premium'];
  return plans[planNumber] || 'Standard';
}

function generateUrlList(urls) {
  if (!urls.length) {
    return '<p>No URLs added yet.</p>';
  }
  
  return urls.map(url => `
    <div class="url-item" data-url='${JSON.stringify(url)}' style="cursor: pointer;">
      <div class="url-info">
        <span class="url-address">${url.url}</span>
        <span class="url-status ${url.isActive ? 'active' : 'inactive'}">
          ${url.isActive ? 'Active' : 'Inactive'}
        </span>
        ${url.checkSeo ? '<span class="url-seo">SEO</span>' : ''}
      </div>
      <div class="url-frequency">
        Checking: ${url.frequency}
      </div>
    </div>
  `).join('');
}

document.addEventListener('DOMContentLoaded', () => {

  const getStartedBtn = document.getElementById('getStartedBtn');
  const signupBtn = document.getElementById('signupBtn');
  const signupPopup = document.getElementById('signupPopup');
  const closePopup = document.getElementById('closePopup');
  const urlsContainer = document.getElementById('urlsContainer');
  const addUrlBtn = document.getElementById('addUrlBtn');
  
  const differentContactCheckbox = document.getElementById('differentContactEmail');
  const contactEmailContainer = document.getElementById('contactEmailContainer');
  const contactEmailInput = document.getElementById('contactEmail');
  const addContactBtn = document.getElementById('addContactBtn');
  const contactsContainer = document.getElementById('contactsContainer');


  const telegramTutorialBtn = document.getElementById('telegramTutorialBtn');
  const telegramHelpBtn = document.getElementById('telegramHelpBtn');
  const telegramTutorialPopup = document.getElementById('telegramTutorialPopup');
  const closeTelegramTutorial = document.getElementById('closeTelegramTutorial');

  setupUrlInputs();
  
  const observer = new MutationObserver(mutations => {
    let shouldSetup = false;
    
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === 1) {
            if (node.tagName === 'INPUT' && node.type === 'url') {
              shouldSetup = true;
            } else if (node.querySelectorAll) {
              const urlInputs = node.querySelectorAll('input[type="url"]');
              if (urlInputs.length > 0) {
                shouldSetup = true;
              }
            }
          }
        });
      }
    });
    
    if (shouldSetup) {
      setupUrlInputs();
    }
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
  
  if (differentContactCheckbox && contactEmailContainer && contactEmailInput) {
    differentContactCheckbox.addEventListener('change', () => {
      const contactHelpText = document.getElementById('contactHelpText');
      contactEmailContainer.style.display = differentContactCheckbox.checked ? 'block' : 'none';
      contactEmailInput.required = differentContactCheckbox.checked;
      if (contactHelpText) {
        contactHelpText.style.display = differentContactCheckbox.checked ? 'block' : 'none';
      }
    });
  }
  
  if (addContactBtn && contactsContainer) {
    addContactBtn.addEventListener('click', () => {
      const contactWrapper = document.createElement('div');
      contactWrapper.style.display = 'flex';
      contactWrapper.style.gap = '10px';
      contactWrapper.style.alignItems = 'center';
      contactWrapper.style.marginBottom = '10px';

      const newContactInput = document.createElement('input');
      newContactInput.type = 'text';
      newContactInput.name = 'contactEmail';
      newContactInput.placeholder = 'Additional Email or Telegram ID';
      newContactInput.style.padding = '10px';
      newContactInput.style.border = '1px solid #ccc';
      newContactInput.style.borderRadius = '6px';
      newContactInput.style.fontSize = '1rem';
      newContactInput.style.flex = '1';

      const removeBtn = document.createElement('button');
      removeBtn.type = 'button';
      removeBtn.textContent = '✕';
      removeBtn.style.background = '#f44336';
      removeBtn.style.color = 'white';
      removeBtn.style.border = 'none';
      removeBtn.style.padding = '10px 12px';
      removeBtn.style.borderRadius = '6px';
      removeBtn.style.cursor = 'pointer';
      removeBtn.style.fontSize = '0.8rem';

      removeBtn.addEventListener('click', () => {
        contactWrapper.remove();
      });

      contactWrapper.appendChild(newContactInput);
      contactWrapper.appendChild(removeBtn);
      contactsContainer.appendChild(contactWrapper);
    });
  }
  
  if (addUrlBtn && urlsContainer) {
    addUrlBtn.addEventListener('click', () => {
      const currentLimit = getCurrentPlanLimit();
      const currentCount = urlsContainer.querySelectorAll('div').length;
      const errorMsg = document.getElementById('urlErrorMsg');
    
      if (currentCount >= currentLimit) {
        errorMsg.textContent = `You have reached the limit of ${currentLimit} URLs allowed by the selected plan.`;
        errorMsg.style.display = 'block';
        return;
      }
    
      errorMsg.style.display = 'none';
      urlsContainer.appendChild(createUrlItem());
    });
  }
  
  if (getStartedBtn && signupPopup) {
    getStartedBtn.addEventListener('click', e => {
      e.preventDefault();
      signupPopup.style.display = 'flex';
    });
  }
  
  if (signupBtn && signupPopup) {
    signupBtn.addEventListener('click', e => {
      e.preventDefault();
      console.log('Signup button clicked, showing popup');
      signupPopup.style.display = 'flex';
    });
  } else {
    console.error('signupBtn or signupPopup not found:', { signupBtn, signupPopup });
  }
  
  if (closePopup && signupPopup) {
    closePopup.addEventListener('click', () => {
      signupPopup.style.display = 'none';
    });
  }
  
  if (signupPopup) {
    signupPopup.addEventListener('click', e => {
      if (e.target === signupPopup) {
        signupPopup.style.display = 'none';
      }
    });
  }


  if (telegramTutorialBtn && telegramTutorialPopup) {
    telegramTutorialBtn.addEventListener('click', e => {
      e.preventDefault();
      telegramTutorialPopup.style.display = 'flex';
    });
  }

  if (telegramHelpBtn && telegramTutorialPopup) {
    telegramHelpBtn.addEventListener('click', e => {
      e.preventDefault();
      telegramTutorialPopup.style.display = 'flex';
    });
  }

  if (closeTelegramTutorial && telegramTutorialPopup) {
    closeTelegramTutorial.addEventListener('click', () => {
      telegramTutorialPopup.style.display = 'none';
    });
  }

  if (telegramTutorialPopup) {
    telegramTutorialPopup.addEventListener('click', e => {
      if (e.target === telegramTutorialPopup) {
        telegramTutorialPopup.style.display = 'none';
      }
    });
  }
  
  const signupForm = document.getElementById('signupForm');
  console.log('signupForm element found:', signupForm);

  if (signupForm) {
    console.log('Adding submit event listener to signupForm');

    // Also add a click listener to the register button for debugging
    const registerButton = signupForm.querySelector('button[type="submit"]');
    if (registerButton) {
      console.log('Register button found, adding click listener');
      registerButton.addEventListener('click', (e) => {
        console.log('Register button clicked');
      });
    } else {
      console.error('Register button not found in form');
    }

    signupForm.addEventListener('submit', async e => {
      console.log('Form submit event triggered');
      e.preventDefault();

      // Get the submit button and store original state
      const submitButton = e.target.querySelector('button[type="submit"]');
      console.log('Submit button found:', submitButton);

      if (!submitButton) {
        console.error('Submit button not found');
        return;
      }

      const originalButtonText = submitButton.textContent;

      // Function to restore button state
      const restoreButton = () => {
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;
        submitButton.style.opacity = '1';
        submitButton.style.cursor = 'pointer';
      };

      // Set loading state
      submitButton.textContent = 'Registering...';
      submitButton.disabled = true;
      submitButton.style.opacity = '0.7';
      submitButton.style.cursor = 'wait';

      const formData = new FormData(e.target);

      // Validate required fields
      const name = formData.get('name');
      const loginEmail = formData.get('loginEmail');
      const password = formData.get('password');
      const plan = formData.get('plan');

      console.log('Form data:', { name, loginEmail, password, plan });

      if (!name || !loginEmail || !password || !plan) {
        console.log('Validation failed - missing required fields');
        alert('Please fill in all required fields');
        restoreButton();
        return;
      }

      console.log('Form validation passed');

      const urlInputs = e.target.querySelectorAll('input[name="urls[]"]');
      const frequencySelects = e.target.querySelectorAll('select[name="frequencies[]"]');
      const seoCheckboxes = e.target.querySelectorAll('input[name="checkSeo"]');

      const urls = [];
      const selectedPlan = plan.toLowerCase();
      
      for (let i = 0; i < urlInputs.length; i++) {
        if (urlInputs[i].value && frequencySelects[i].value) {
          urls.push({
            url: urlInputs[i].value,
            frequency: frequencySelects[i].value,
            isActive: true,
            checkSeo: selectedPlan === 'standard' ? false : (seoCheckboxes[i]?.checked ?? true)
          });
        }
      }
      
      if (urls.length === 0) {
        alert('Please add at least one URL');
        restoreButton();
        return;
      }

      // Handle contact information
      let contacts = [];
      const differentContactCheckbox = document.getElementById('differentContactEmail');

      if (differentContactCheckbox && differentContactCheckbox.checked) {
        const contactInputs = e.target.querySelectorAll('input[name="contactEmail"]');
        contactInputs.forEach(input => {
          if (input.value) {
            contacts.push(input.value.trim());
          }
        });

        if (contacts.length === 0) {
          alert('Please add at least one contact email');
          restoreButton();
          return;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(contacts[0])) {
          alert('Primary contact must be a email');
          restoreButton();
          return;
        }
      } else {
        contacts.push(formData.get('loginEmail'));
      }
      
      const data = {
        name: formData.get('name'),
        authInfos: { email: formData.get('loginEmail') },
        contact: contacts,
        password: formData.get('password'),
        plan: planMap[formData.get('plan').toLowerCase()],
        urls: urls
      };
      
      try {
        console.log('Submitting registration data:', data);
        const result = await callApi(API_CONFIG.ENDPOINTS.CREATE_CLIENT, data);
        console.log('Registration successful:', result);

        // Restore button state
        restoreButton();

        alert('Registration successful!');

        // Reset form and UI
        signupForm.reset();

        const urlsContainer = document.getElementById('urlsContainer');
        const contactsContainer = document.getElementById('contactsContainer');
        const contactEmailContainer = document.getElementById('contactEmailContainer');
        const signupPopup = document.getElementById('signupPopup');

        if (urlsContainer) {
          urlsContainer.innerHTML = '';
          urlsContainer.appendChild(createUrlItem());
        }

        if (contactsContainer) {
          contactsContainer.innerHTML = '<div style="display:flex; gap:10px; align-items:center; margin-bottom:10px;"><input type="email" id="contactEmail" name="contactEmail" placeholder="Contact Email (required)" style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem; flex:1;" required></div>';
        }

        if (contactEmailContainer) {
          contactEmailContainer.style.display = 'none';
        }

        if (signupPopup) {
          signupPopup.style.display = 'none';
        }

      } catch (error) {
        // Restore button state on error
        restoreButton();

        alert(`Error: ${error.message}`);
        console.error('Registration error:', error);
      }
    });
  }

  const planSelect = document.querySelector('select[name="plan"]');
  if (planSelect) {
    planSelect.addEventListener('change', () => {
      const currentLimit = getCurrentPlanLimit();
      const currentCount = urlsContainer.querySelectorAll('div').length;
      if (currentCount > currentLimit) {
        alert(`You already added more URLs than allowed by the selected plan (${currentLimit}).`);
      }
      

      const selectedPlan = planSelect.value.toLowerCase();
      const seoCheckboxes = document.querySelectorAll('.checkbox-group');
      seoCheckboxes.forEach(checkbox => {
        if (checkbox.querySelector('input[name="checkSeo"]')) {
          checkbox.style.display = selectedPlan === 'standard' ? 'none' : 'block';

          if (selectedPlan === 'standard') {
            checkbox.querySelector('input[name="checkSeo"]').checked = false;
          }
        }
      });
    });
  }

  if (urlsContainer) {
    urlsContainer.appendChild(createUrlItem());
  }

  const loginBtn = document.getElementById('loginBtn');
  const loginPopup = document.getElementById('loginPopup');
  const closeLoginPopup = document.getElementById('closeLoginPopup');

  loginBtn.addEventListener('click', e => {
    e.preventDefault();
    loginPopup.style.display = 'flex';
  });

  closeLoginPopup.addEventListener('click', () => {
    loginPopup.style.display = 'none';
  });

  loginPopup.addEventListener('click', e => {
    if (e.target === loginPopup) {
      loginPopup.style.display = 'none';
    }
  });
  
  document.getElementById('loginForm').addEventListener('submit', async e => {
    e.preventDefault();

    const formData = new FormData(e.target);
    
    const loginData = {
      email: formData.get('email'),
      password: formData.get('password')
    };

    try {
      const response = await callApi(API_CONFIG.ENDPOINTS.LOGIN, loginData);
      
      const token = response.token;
      
      if (!token) {
        throw new Error('No token received from server');
      }
      
      localStorage.setItem('userToken', token);
      
      const userData = parseJwt(token);
      
      localStorage.setItem('userData', JSON.stringify(userData));
      
      loginPopup.style.display = 'none';
      
      showDashboard(userData);
      
    } catch (error) {
      alert(`Error: ${error.message}`);
      console.error('Login error:', error);
    }
  });

  function showDashboard(userData) {
    document.querySelectorAll('section, header, footer, .top-bar').forEach(el => {
      el.style.display = 'none';
    });
    
    const dashboardContainer = document.createElement('div');
    dashboardContainer.id = 'dashboard';
    dashboardContainer.className = 'dashboard-container';
    
    dashboardContainer.innerHTML = `
      <div class="dashboard-header">
        <h1>Welcome, ${userData.name || 'User'}</h1>
        <div style="display: flex; gap: 10px; align-items: center;">
          <button id="supportBtn" class="support-button" style="background:#4caf50; color:white; border:none; padding:8px 16px; border-radius:6px; cursor:pointer; font-size:0.9rem;">📧 Support</button>
          <button id="logoutBtn" class="logout-button">Logout</button>
        </div>
      </div>
      <div class="dashboard-tabs">
        <button class="tab-button active" data-tab="urls">URLs</button>
        <button class="tab-button" data-tab="info">Account Info</button>
        <button class="tab-button" data-tab="executions">Executions Report</button>
      </div>
      <div class="dashboard-content">
        <div class="dashboard-sidebar">
          <h3>Loading your plan...</h3>
          <p>Contact: ${userData.contact || 'Not available'}</p>
        </div>
        <div class="dashboard-main">
          <div id="urls-tab" class="tab-content active">
            <h2>Your Monitored URLs</h2>
            <div class="url-list">
              <p>Loading your URLs...</p>
            </div>
          </div>
          <div id="info-tab" class="tab-content">
            <h2>Account Information</h2>
            <div class="account-info">
              <p>Loading account information...</p>
            </div>
          </div>
          <div id="executions-tab" class="tab-content">
            <h2>Executions Report</h2>
            <div class="executions-filter">
              <div class="date-filters">
                <label>
                  Start Date:
                  <input type="date" id="startDate" name="startDate">
                </label>
                <label>
                  End Date:
                  <input type="date" id="endDate" name="endDate">
                </label>
                <button id="searchExecutions" class="fetch-button search-button">Search</button>
              </div>
            </div>
            <div class="executions-results">
              <p>Select a date range and click Search to view execution reports.</p>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(dashboardContainer);
    
    document.getElementById('supportBtn').addEventListener('click', () => {
      window.location.href = 'mailto:<EMAIL>?subject=Sasquat Support Request';
    });

    document.getElementById('logoutBtn').addEventListener('click', () => {
      localStorage.removeItem('userToken');
      localStorage.removeItem('userData');

      document.body.removeChild(dashboardContainer);
      
      document.querySelectorAll('section, header, footer, .top-bar').forEach(el => {
        el.style.display = '';
      });
    });

    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        button.classList.add('active');
        const tabName = button.getAttribute('data-tab');
        document.getElementById(`${tabName}-tab`).classList.add('active');
      });
    });
    
    const today = new Date();
    const lastWeek = new Date();
    lastWeek.setDate(today.getDate() - 7);
    
    document.getElementById('startDate').valueAsDate = lastWeek;
    document.getElementById('endDate').valueAsDate = today;
    
    document.getElementById('searchExecutions').addEventListener('click', async () => {
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      
      if (!startDate || !endDate) {
        alert('Please select both start and end dates');
        return;
      }
      
      try {
        const resultsContainer = document.querySelector('.executions-results');
        resultsContainer.innerHTML = '<p>Loading execution data...</p>';
        
        await fetchExecutionsReport(userData.id, startDate, endDate);
      } catch (error) {
        console.error('Error fetching executions:', error);
      }
    });
    
    fetchClientData(userData);
    
    async function fetchClientData(basicUserData) {
      try {
        const token = localStorage.getItem('userToken');
        if (!token) {
          throw new Error('No authentication token found');
        }
        
        const queryParams = new URLSearchParams({
          id: basicUserData.id || '',
          name: basicUserData.name || '',
          contact: basicUserData.email || ''
        }).toString();
        
        const endpoint = `${API_CONFIG.ENDPOINTS.SEARCH_CLIENT}?${queryParams}`;
        const clientData = await callGetApi(endpoint, null, token);

        const updatedUserData = {
          ...basicUserData,
          plan: clientData.plan || 0
        };
        
        localStorage.setItem('userData', JSON.stringify(updatedUserData));

        updateDashboardWithClientData(clientData);
      } catch (error) {
        console.error('Error fetching client data:', error);
        document.querySelector('.dashboard-sidebar h3').textContent = 'Failed to load plan';
        document.querySelector('.url-list').innerHTML = 
          '<p>Failed to load your URLs. Please try logging in again.</p>';
      }
    }
    
    async function fetchExecutionsReport(clientId, startDate, endDate) {
      try {
        const token = localStorage.getItem('userToken');
        if (!token) {
          throw new Error('No authentication token found');
        }
        
        const queryParams = new URLSearchParams({
          idClient: clientId,
          startDate: startDate,
          endDate: endDate
        }).toString();
        
        const endpoint = `${API_CONFIG.ENDPOINTS.GET_EXECUTIONS}?${queryParams}`;
        const executionsData = await callGetApi(endpoint, null, token);
        
        updateExecutionsResults(executionsData);
      } catch (error) {
        console.error('Error fetching executions data:', error);
        document.querySelector('.executions-results').innerHTML = 
          '<p class="error">Failed to load executions. Please try again.</p>';
      }
    }
    
    function updateExecutionsResults(executionsData) {
      const resultsContainer = document.querySelector('.executions-results');

      if (!executionsData || executionsData.length === 0) {
        resultsContainer.innerHTML = '<p>No execution data found for the selected date range.</p>';
        return;
      }

      const escapeHtml = (input) => {
        if (!input) return '';
        return input
          .replace(/&/g, "&amp;")
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;');
      };

      const uniqueUrls = [...new Set(executionsData.map(item => item.url))];
      const totalChecks = executionsData.length;
      const successfulChecks = executionsData.filter(item => item.urlCheckSuccess).length;
      const failedChecks = totalChecks - successfulChecks;
      const successRate = Math.round((successfulChecks / totalChecks) * 100);

      const sortedExecutions = [...executionsData].sort((a, b) =>
        new Date(b.executionTime) - new Date(a.executionTime)
      );

      const urlFilterHtml = `
        <div class="url-filter">
          <label for="urlFilter">Filter by URL:</label>
          <select id="urlFilter">
            <option value="all">All URLs (${formatNumberWithPeriods(totalChecks)} checks)</option>
            ${uniqueUrls.map(url => {
              const count = executionsData.filter(item => item.url === url).length;
              return `<option value="${url}">${url} (${formatNumberWithPeriods(count)} checks)</option>`;
            }).join('')}
          </select>
        </div>
      `;


      const statusChartHtml = `
        <div class="status-chart">
          <h3>Status Distribution</h3>
          <div class="chart-container">
            <div class="chart-bar">
              <div class="chart-segment success" style="width: ${successRate}%;" title="${formatNumberWithPeriods(successfulChecks)} successful checks">
                ${successRate > 10 ? `${successRate}%` : ''}
              </div>
              <div class="chart-segment failure" style="width: ${100 - successRate}%;" title="${formatNumberWithPeriods(failedChecks)} failed checks">
                ${(100 - successRate) > 10 ? `${100 - successRate}%` : ''}
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color success"></span>
                <span>Success: ${formatNumberWithPeriods(successfulChecks)} (${successRate}%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color failure"></span>
                <span>Failure: ${formatNumberWithPeriods(failedChecks)} (${100 - successRate}%)</span>
              </div>
            </div>
          </div>
        </div>
      `;


      const urlStats = {};
      uniqueUrls.forEach(url => {
        const urlData = executionsData.filter(item => item.url === url);
        const urlSuccessCount = urlData.filter(item => item.urlCheckSuccess).length;
        urlStats[url] = {
          success: urlSuccessCount,
          failure: urlData.length - urlSuccessCount
        };
      });


      const advancedChartsHtml = `
        <div class="advanced-charts">
          <div class="chart-row">
            <div class="chart-column">
              <h3>Status Distribution</h3>
              <div class="chart-wrapper">
                <canvas id="pieChart" width="200" height="200"></canvas>
              </div>
            </div>
            <div class="chart-column">
              <h3>URL Performance</h3>
              <div class="chart-wrapper">
                <canvas id="barChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
          <div class="chart-row">
            <div class="chart-column full-width">
              <h3>Status Timeline</h3>
              <div class="chart-wrapper">
                <canvas id="lineChart" width="800" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>
      `;

      const executionsListHtml = `
        <div class="archives-list" id="executionsList">
          ${sortedExecutions.map(execution => {
            const executionDate = new Date(execution.executionTime).toLocaleString();
            const statusClass = execution.urlCheckSuccess ? 'status-up' : 'status-down';
            const statusText = execution.urlCheckSuccess ? 'UP' : 'DOWN';

            const httpErrorHtml = !execution.urlCheckSuccess
              ? `<div class="archive-message">HTTP Error: ${execution.errorMessageHttp || 'Unknown error'}</div>`
              : '';

            const sslErrorHtml = execution.sslCheckPerformed && !execution.sslCheckSuccess
              ? `<div class="archive-message">SSL Error: ${execution.errorMessageSSL || 'SSL validation failed'}</div>`
              : '';

            const seoIssuesHtml = execution.seoCheckPerformed && execution.seoCheckIssues
              ? `<div class="archive-message">SEO Issues: <code>${escapeHtml(execution.seoCheckIssues)}</code></div>`
              : '';

            return `
              <div class="archive-item" data-url="${execution.url}">
                <div class="archive-header">
                  <span class="archive-url">${execution.url}</span>
                  <span class="archive-status ${statusClass}">${statusText}</span>
                </div>
                <div class="archive-details">
                  <span>Date: ${executionDate}</span>
                </div>
                ${httpErrorHtml}
                ${sslErrorHtml}
                ${seoIssuesHtml}
              </div>
            `;
          }).join('')}
        </div>
      `;

      const exportButtonHtml = `
        <div class="export-controls">
          <button id="exportPdfBtn" class="fetch-button">Export as PDF</button>
          <span id="exportStatus" style="display: none; margin-left: 10px;"></span>
        </div>
      `;

      resultsContainer.innerHTML = `
        <div class="executions-summary">
          ${statusChartHtml}
          ${urlFilterHtml}
          ${exportButtonHtml}
        </div>
        ${advancedChartsHtml}
        ${executionsListHtml}
      `;


      initializeCharts(executionsData, uniqueUrls, urlStats, successfulChecks, failedChecks);

      const urlFilter = document.getElementById('urlFilter');
      if (urlFilter) {
        urlFilter.addEventListener('change', function () {
          const selectedUrl = this.value;
          const items = document.querySelectorAll('#executionsList .archive-item');

          items.forEach(item => {
            if (selectedUrl === 'all' || item.dataset.url === selectedUrl) {
              item.style.display = '';
            } else {
              item.style.display = 'none';
            }
          });


          if (selectedUrl === 'all') {
            initializeCharts(executionsData, uniqueUrls, urlStats, successfulChecks, failedChecks);
          } else {
            const filteredData = executionsData.filter(item => item.url === selectedUrl);
            const filteredSuccess = filteredData.filter(item => item.urlCheckSuccess).length;
            const filteredFailure = filteredData.length - filteredSuccess;
            
            const filteredUrlStats = {};
            filteredUrlStats[selectedUrl] = urlStats[selectedUrl];
            
            initializeCharts(filteredData, [selectedUrl], filteredUrlStats, filteredSuccess, filteredFailure);
          }
        });
      }

      const exportPdfBtn = document.getElementById('exportPdfBtn');
      if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', async function () {
          try {
            const exportStatus = document.getElementById('exportStatus');
            exportStatus.textContent = 'Generating PDF...';
            exportStatus.style.display = 'inline';

            await addPdfLibrary();

            const selectedUrl = urlFilter.value;
            const filterText = selectedUrl === 'all' ? 'All URLs' : selectedUrl;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            await generateExecutionsPdf(executionsData, filterText, startDate, endDate, selectedUrl);

            exportStatus.textContent = 'PDF generated successfully!';
            setTimeout(() => {
              exportStatus.style.display = 'none';
            }, 3000);
          } catch (error) {
            console.error('Error generating PDF:', error);
            const exportStatus = document.getElementById('exportStatus');
            exportStatus.textContent = 'Error generating PDF. Please try again.';
            exportStatus.style.color = 'red';
            exportStatus.style.display = 'inline';
          }
        });
      }
    }


    function initializeCharts(executionsData, uniqueUrls, urlStats, successCount, failureCount) {

      if (typeof Chart === 'undefined') {
        loadChartJs().then(() => {
          renderCharts(executionsData, uniqueUrls, urlStats, successCount, failureCount);
        });
      } else {
        renderCharts(executionsData, uniqueUrls, urlStats, successCount, failureCount);
      }
    }


    function loadChartJs() {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }


    function renderCharts(executionsData, uniqueUrls, urlStats, successCount, failureCount) {
      Chart.getChart('pieChart')?.destroy();
      Chart.getChart('barChart')?.destroy();
      Chart.getChart('lineChart')?.destroy();
      const pieCtx = document.getElementById('pieChart').getContext('2d');
      new Chart(pieCtx, {
        type: 'pie',
        data: {
          labels: ['Success', 'Failure'],
          datasets: [{
            data: [successCount, failureCount],
            backgroundColor: ['#4CAF50', '#F44336'],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });


      const barCtx = document.getElementById('barChart').getContext('2d');
      new Chart(barCtx, {
        type: 'bar',
        data: {
          labels: uniqueUrls.map(url => url.length > 20 ? url.substring(0, 20) + '...' : url),
          datasets: [
            {
              label: 'Success',
              data: uniqueUrls.map(url => urlStats[url].success),
              backgroundColor: '#4CAF50'
            },
            {
              label: 'Failure',
              data: uniqueUrls.map(url => urlStats[url].failure),
              backgroundColor: '#F44336'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              stacked: true
            },
            y: {
              stacked: true,
              beginAtZero: true
            }
          },
          plugins: {
            legend: {
              position: 'bottom'
            },
            tooltip: {
              callbacks: {
                title: function(tooltipItems) {
                  const index = tooltipItems[0].dataIndex;
                  return uniqueUrls[index];
                }
              }
            }
          }
        }
      });


      const timelineData = prepareTimelineData(executionsData);
      const lineCtx = document.getElementById('lineChart').getContext('2d');
      new Chart(lineCtx, {
        type: 'line',
        data: {
          labels: timelineData.labels,
          datasets: timelineData.datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 1,
              ticks: {
                callback: function(value) {
                  return value === 1 ? 'UP' : 'DOWN';
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom'
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const status = context.raw === 1 ? 'UP' : 'DOWN';
                  return `${context.dataset.label}: ${status}`;
                }
              }
            }
          }
        }
      });
    }

    function prepareTimelineData(executionsData) {
      const urlGroups = {};
      executionsData.forEach(execution => {
        if (!urlGroups[execution.url]) {
          urlGroups[execution.url] = [];
        }
        urlGroups[execution.url].push({
          time: new Date(execution.executionTime),
          status: execution.urlCheckSuccess ? 1 : 0
        });
      });


      Object.keys(urlGroups).forEach(url => {
        urlGroups[url].sort((a, b) => a.time - b.time);
      });


      const allTimes = executionsData.map(e => new Date(e.executionTime).getTime());
      const uniqueTimes = [...new Set(allTimes)].sort();


      const labels = uniqueTimes.map(time => {
        const date = new Date(time);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      });


      const datasets = Object.keys(urlGroups).map((url, index) => {
        const color = getColorForIndex(index);
        const data = [];
        

        uniqueTimes.forEach(time => {
          const closest = findClosestDataPoint(urlGroups[url], time);
          data.push(closest ? closest.status : null);
        });
        
        return {
          label: url.length > 30 ? url.substring(0, 30) + '...' : url,
          data: data,
          borderColor: color,
          backgroundColor: color + '33',
          fill: false,
          tension: 0.1,
          pointRadius: 4,
          pointHoverRadius: 6
        };
      });

      return { labels, datasets };
    }

    function findClosestDataPoint(dataPoints, targetTime) {
      if (dataPoints.length === 0) return null;
      let closest = dataPoints[0];
      let closestDiff = Math.abs(closest.time.getTime() - targetTime);
      
      for (let i = 1; i < dataPoints.length; i++) {
        const diff = Math.abs(dataPoints[i].time.getTime() - targetTime);
        if (diff < closestDiff) {
          closest = dataPoints[i];
          closestDiff = diff;
        }
      }
      

      return closestDiff < 12 * 60 * 60 * 1000 ? closest : null;
    }


    function getColorForIndex(index) {
      const colors = [
        '#4CAF50', '#2196F3', '#9C27B0', '#FF9800', '#795548',
        '#607D8B', '#E91E63', '#3F51B5', '#009688', '#FFC107'
      ];
      return colors[index % colors.length];
    }

    async function addPdfLibrary() {
      if (typeof window.jspdf === 'undefined' || typeof window.jspdf.jsPDF === 'undefined') {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
          script.onload = () => {
            try {
              window.jsPDF = window.jspdf.jsPDF;
              resolve();
            } catch (e) {
              reject(new Error("Failed to initialize jsPDF"));
            }
          };
          script.onerror = () => reject(new Error("Failed to load jsPDF library"));
          document.head.appendChild(script);
        });
      } else {
        window.jsPDF = window.jspdf.jsPDF;
      }
    }


    async function generateExecutionsPdf(data, filterText, startDate, endDate, selectedUrl) {
      const { jsPDF } = window.jspdf;
      const doc = new jsPDF();

      doc.setFontSize(16);
      doc.text(`Execution Report: ${filterText}`, 10, 20);
      doc.setFontSize(12);
      doc.text(`From: ${startDate} To: ${endDate}`, 10, 30);

      const filteredData = data.filter(item => selectedUrl === 'all' || item.url === selectedUrl);
      const urlStats = {};
      let successCount = 0;
      let failureCount = 0;

      filteredData.forEach(item => {
        if (!urlStats[item.url]) urlStats[item.url] = { success: 0, failure: 0 };
        if (item.urlCheckSuccess) {
          urlStats[item.url].success++;
          successCount++;
        } else {
          urlStats[item.url].failure++;
          failureCount++;
        }
      });


      const barCanvas = document.createElement('canvas');
      barCanvas.width = 400;
      barCanvas.height = 200;
      document.body.appendChild(barCanvas);

      const barChart = new Chart(barCanvas, {
        type: 'bar',
        data: {
          labels: Object.keys(urlStats),
          datasets: [
            {
              label: 'Success',
              backgroundColor: '#4CAF50',
              data: Object.values(urlStats).map(u => u.success),
            },
            {
              label: 'Failure',
              backgroundColor: '#F44336',
              data: Object.values(urlStats).map(u => u.failure),
            }
          ]
        },
        options: {
          responsive: false,
          animation: false,
          plugins: {
            legend: { position: 'top' }
          }
        }
      });

      await new Promise(resolve => setTimeout(resolve, 500));

      const barImg = await html2canvas(barCanvas).then(c => c.toDataURL('image/png'));
      doc.addImage(barImg, 'PNG', 10, 40, 180, 90);
      document.body.removeChild(barCanvas);

      let y = 140;

      const pieCanvas = document.createElement('canvas');
      pieCanvas.width = 200;
      pieCanvas.height = 200;
      document.body.appendChild(pieCanvas);

      new Chart(pieCanvas, {
        type: 'pie',
        data: {
          labels: ['Success', 'Failure'],
          datasets: [{
            data: [successCount, failureCount],
            backgroundColor: ['#4CAF50', '#F44336']
          }]
        },
        options: {
          responsive: false,
          animation: false
        }
      });

      await new Promise(resolve => setTimeout(resolve, 500));

      const pieImg = await html2canvas(pieCanvas).then(c => c.toDataURL('image/png'));
      doc.addImage(pieImg, 'PNG', 10, y, 100, 80);
      document.body.removeChild(pieCanvas);
      y += 90;

      const tableData = filteredData.map((item, i) => [
        i + 1,
        item.url,
        item.executionTime,
        item.urlCheckSuccess ? 'UP' : 'DOWN',
        item.seoCheckPerformed ? (item.seoCheckIssues || '-') : 'Not Performed'
      ]);

      doc.autoTable({
        startY: y,
        head: [['#', 'URL', 'Exec Time', 'Status', 'SEO Issues']],
        body: tableData,
        styles: { fontSize: 8 },
        theme: 'striped'
      });

      const endY = doc.lastAutoTable.finalY + 10;
      doc.setFontSize(12);
      doc.text('Summary:', 10, endY);
      doc.setFontSize(10);
      doc.text(`Total executions: ${filteredData.length}`, 10, endY + 10);
      doc.text(`Successes: ${successCount}`, 10, endY + 20);
      doc.text(`Failures: ${failureCount}`, 10, endY + 30);

      doc.save(`executions_${startDate}_to_${endDate}.pdf`);
    }
    
    function updateDashboardWithClientData(clientData) {
      const sidebarEl = document.querySelector('.dashboard-sidebar');
      
      sidebarEl.innerHTML = `
        <h3>Your Plan: ${getPlanName(clientData.plan)}</h3>
        <p>Primary Contact: ${clientData.contact[0]}</p>
        <p>Credits: ${formatNumberWithPeriods(clientData.credits) || 'Not available'}</p>
        <p>Account Status: 
          <span class="url-status ${clientData.isActive ? 'active' : 'inactive'}">
              ${clientData.isActive ? 'Active' : 'Inactive'}
          </span>
          <p class="status-help">
            ${clientData.isActive ? '' : 'If you are having trouble with your account activation, please contact us.'}
          </p>
        </p>
        <button id="addCreditsButton" class="fetch-button" style="margin-bottom: 10px;">Add Credits</button>
        <button id="addUrlButton" class="fetch-button">Add New URL</button>
      `;
      
      document.getElementById('addCreditsButton').addEventListener('click', () => {
        showAddCreditsPopup(clientData);
      });
      
      const urlListEl = document.querySelector('.url-list');
      urlListEl.innerHTML = generateUrlList(clientData.urls || []);
      
      const accountInfoEl = document.querySelector('.account-info');
      
      let contactsHtml = '';
      if (clientData.contact && Array.isArray(clientData.contact) && clientData.contact.length > 0) {
        clientData.contact.forEach((contact, index) => {
          const showRemoveBtn = clientData.contact.length > 1 && index > 0;
          const inputType = index === 0 ? 'email' : 'text';
          const placeholder = index === 0 ? 'Contact Email (required)' : 'Additional Email or Telegram ID';
          const required = index === 0 ? 'required' : '';
          contactsHtml += `
            <div style="display:flex; gap:10px; align-items:center; margin-bottom:10px;">
              <input type="${inputType}" name="contactEmail" value="${contact}"
                    placeholder="${placeholder}" ${required}
                    style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem; flex:1;">
              ${showRemoveBtn ? '<button type="button" class="remove-contact-btn" style="background:#f44336; color:white; border:none; padding:10px 12px; border-radius:6px; cursor:pointer; font-size:0.8rem;">✕</button>' : ''}
            </div>
          `;
        });
      } else if (clientData.contact) {
        contactsHtml = `
          <div style="display:flex; gap:10px; align-items:center; margin-bottom:10px;">
            <input type="email" name="contactEmail" value="${clientData.contact}"
                  placeholder="Contact Email (required)" required
                  style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem; flex:1;">
          </div>
        `;
      } else {
        contactsHtml = `
          <div style="display:flex; gap:10px; align-items:center; margin-bottom:10px;">
            <input type="email" name="contactEmail" placeholder="Contact Email (required)" required
                  style="padding:10px; border:1px solid #ccc; border-radius:6px; font-size:1rem; flex:1;">
          </div>
        `;
      }

      accountInfoEl.innerHTML = `
        <div class="info-card">
          <h3>Account Details</h3>
          <form id="updateAccountForm">
            <div class="form-group">
              <label for="updateName">Name:</label>
              <input type="text" id="updateName" name="name" value="${clientData.name}" required>
            </div>
            <div class="form-group">
              <label for="updateEmail">Login Email:</label>
              <input type="email" id="updateEmail" name="email" value="${clientData.authInfos?.email || ''}" readonly>
            </div>
            <div class="form-group">
              <label for="updateContact">Contact Information:</label>
              <small style="color:#666; display:block; margin-bottom:5px;">First contact must be an email address. Additional contacts can be emails or Telegram IDs.</small>
              <div id="updateContactsContainer">
                ${contactsHtml}
              </div>
              <div style="display:flex; gap:10px; align-items:center; margin-top:5px;">
                <button type="button" id="updateAddContactBtn" class="fetch-button" style="font-size:0.9rem;">+ Add Another Contact</button>
                <button type="button" id="dashboardTelegramHelpBtn" class="tutorial-btn" style="font-size:0.8rem; padding:6px 12px;">📱 Telegram Setup</button>
              </div>
            </div>
            <div class="form-group">
              <label for="updatePlan">Plan:</label>
              <select id="updatePlan" name="plan" disabled>
                <option value="0" ${clientData.plan === 0 ? 'selected' : ''}>Standard</option>
                <option value="1" ${clientData.plan === 1 ? 'selected' : ''}>Plus</option>
                <option value="2" ${clientData.plan === 2 ? 'selected' : ''}>Premium</option>
              </select>
              <small>To change your plan, please contact support.</small>
            </div>
            <button type="submit" class="fetch-button">Update Account</button>
          </form>
        </div>
      `;
      
      document.getElementById('updateAddContactBtn').addEventListener('click', () => {
        const updateContactsContainer = document.getElementById('updateContactsContainer');

        const contactWrapper = document.createElement('div');
        contactWrapper.style.display = 'flex';
        contactWrapper.style.gap = '10px';
        contactWrapper.style.alignItems = 'center';
        contactWrapper.style.marginBottom = '10px';

        const newContactInput = document.createElement('input');
        newContactInput.type = 'text';
        newContactInput.name = 'contactEmail';
        newContactInput.placeholder = 'Additional Email or Telegram ID';
        newContactInput.style.padding = '10px';
        newContactInput.style.border = '1px solid #ccc';
        newContactInput.style.borderRadius = '6px';
        newContactInput.style.fontSize = '1rem';
        newContactInput.style.flex = '1';

        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'remove-contact-btn';
        removeBtn.textContent = '✕';
        removeBtn.style.background = '#f44336';
        removeBtn.style.color = 'white';
        removeBtn.style.border = 'none';
        removeBtn.style.padding = '10px 12px';
        removeBtn.style.borderRadius = '6px';
        removeBtn.style.cursor = 'pointer';
        removeBtn.style.fontSize = '0.8rem';

        removeBtn.addEventListener('click', () => {
          contactWrapper.remove();
        });

        contactWrapper.appendChild(newContactInput);
        contactWrapper.appendChild(removeBtn);
        updateContactsContainer.appendChild(contactWrapper);
      });

      document.getElementById('dashboardTelegramHelpBtn').addEventListener('click', () => {
        const telegramTutorialPopup = document.getElementById('telegramTutorialPopup');
        if (telegramTutorialPopup) {
          telegramTutorialPopup.style.display = 'flex';
        }
      });

      document.querySelectorAll('.remove-contact-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const contactWrapper = e.target.closest('div[style*="display:flex"]');
          if (contactWrapper) {
            contactWrapper.remove();
          }
        });
      });
      
      const addUrlPopup = document.createElement('div');
      addUrlPopup.id = 'addUrlPopup';
      addUrlPopup.className = 'popup-container';
      addUrlPopup.style.display = 'none';

      addUrlPopup.innerHTML = `
        <div class="popup-content">
          <button id="closeAddUrlPopup" class="close-button">&times;</button>
          <h2>Add New URL</h2>
          <form id="addUrlForm">
            <div class="form-group">
              <label for="newUrl">URL:</label>
              <input type="url" id="newUrl" name="url" placeholder="https://example.com" required>
            </div>
            <div class="form-group">
              <label for="urlFrequency">Check Frequency:</label>
              <select id="urlFrequency" name="frequency" required>
                <option value="" disabled selected>Select frequency</option>
                <option value="Daily">Daily</option>
                <option value="Hourly">Hourly</option>
                <option value="H2">Every 2 Hours</option>
                <option value="H6">Every 6 Hours</option>
                <option value="H12">Every 12 Hours</option>
              </select>
            </div>
            <div class="form-group checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" id="urlActive" name="isActive" checked>
                <span>Active</span>
              </label>
            </div>
            <div class="form-group checkbox-group" ${clientData.plan === 0 ? 'style="display:none;"' : ''}>
              <label class="checkbox-label">
                <input type="checkbox" id="urlSeo" name="checkSeo" checked>
                <span>Check SEO</span>
              </label>
            </div>
            <button type="submit" class="fetch-button">Add URL</button>
          </form>
        </div>
      `;

      const popupStyles = document.createElement('style');
      popupStyles.textContent = `
        .popup-container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        }
        
        .popup-content {
          background: #fff;
          border-radius: 12px;
          max-width: 500px;
          width: 95%;
          padding: 30px;
          position: relative;
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }
        
        .close-button {
          position: absolute;
          top: 15px;
          right: 15px;
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #666;
          transition: color 0.2s ease;
        }
        
        .close-button:hover {
          color: #000;
        }
        
        .popup-content h2 {
          color: var(--primary);
          margin-bottom: 20px;
          font-size: 1.8rem;
          text-align: center;
        }
        
        .form-group {
          margin-bottom: 20px;
        }
        
        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: var(--primary);
        }
        
        .form-group input[type="url"],
        .form-group select {
          width: 100%;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-size: 1rem;
          transition: border 0.2s ease;
        }
        
        .form-group input[type="url"]:focus,
        .form-group select:focus {
          border-color: var(--yellow);
          outline: none;
          box-shadow: 0 0 0 2px rgba(239, 201, 91, 0.3);
        }
        
        .checkbox-label {
          display: flex;
          align-items: center;
          cursor: pointer;
          user-select: none;
        }
        
        .checkbox-label input {
          margin-right: 8px;
          width: 18px;
          height: 18px;
        }
        
        .checkbox-label span {
          font-weight: 600;
          color: var(--primary);
        }
        
        .fetch-button {
          background: var(--yellow);
          color: var(--dark);
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          font-size: 1rem;
          width: 100%;
          transition: background 0.2s ease, transform 0.2s ease;
        }
        
        .fetch-button:hover {
          background: var(--yellow-dark);
          transform: translateY(-2px);
        }
      `;

      document.head.appendChild(popupStyles);
      document.body.appendChild(addUrlPopup);
      
      document.getElementById('addUrlButton').addEventListener('click', () => {
        const planLimits = {
          0: 5,
          1: 15,
          2: 30 
        };
        
        const currentLimit = planLimits[clientData.plan] || 5;
        const currentCount = clientData.urls ? clientData.urls.length : 0;
        
        if (currentCount >= currentLimit) {
          alert(`You have reached the limit of ${currentLimit} URLs allowed by your ${getPlanName(clientData.plan)} plan. Please upgrade your plan to add more URLs.`);
          return;
        }
        
        document.getElementById('addUrlPopup').style.display = 'flex';
      });
      
      document.getElementById('closeAddUrlPopup').addEventListener('click', () => {
        document.getElementById('addUrlPopup').style.display = 'none';
      });
      
      document.getElementById('addUrlPopup').addEventListener('click', (e) => {
        if (e.target === document.getElementById('addUrlPopup')) {
          document.getElementById('addUrlPopup').style.display = 'none';
        }
      });
      
      document.getElementById('addUrlForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const token = localStorage.getItem('userToken');
        if (!token) {
          alert('You must be logged in to add URLs.');
          return;
        }
        
        const userData = JSON.parse(localStorage.getItem('userData'));
        if (!userData || !userData.id) {
          alert('User data not found. Please log in again.');
          return;
        }
        
        const formData = new FormData(e.target);
        
        try {
          const queryParams = new URLSearchParams({
            id: userData.id,
            name: userData.name || '',
            contact: userData.email || userData.contact || ''
          }).toString();
          
          const endpoint = `${API_CONFIG.ENDPOINTS.SEARCH_CLIENT}?${queryParams}`;
          const currentClientData = await callGetApi(endpoint, null, token);
          
          const planLimits = {
            0: 5,
            1: 15,
            2: 30
          };
          
          const currentLimit = planLimits[currentClientData.plan] || 5;
          const currentCount = currentClientData.urls ? currentClientData.urls.length : 0;
          
          if (currentCount >= currentLimit) {
            alert(`You have reached the limit of ${currentLimit} URLs allowed by your ${getPlanName(currentClientData.plan)} plan. Please upgrade your plan to add more URLs.`);
            return;
          }
          
          const newUrl = {
            url: formData.get('url'),
            frequency: formData.get('frequency'),
            isActive: formData.get('isActive') === 'on',
            checkSeo: currentClientData.plan === 0 ? false : (formData.get('checkSeo') === 'on')
          };

          const updateData = {
            ...currentClientData,
            urls: [...(currentClientData.urls || []), newUrl],
            updatedAt: new Date().toISOString()
          };
          
          await callPutApi(API_CONFIG.ENDPOINTS.UPDATE_CLIENT, updateData, token);
          
          alert('URL added successfully!');
          document.getElementById('addUrlPopup').style.display = 'none';
          
          e.target.reset();
          
          fetchClientData(userData);
          
        } catch (error) {
          alert(`Error adding URL: ${error.message}`);
          console.error('Error adding URL:', error);
        }
      });
      
      document.getElementById('updateAccountForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const token = localStorage.getItem('userToken');
        if (!token) {
          alert('You must be logged in to update your account.');
          return;
        }
        
        const userData = JSON.parse(localStorage.getItem('userData'));
        if (!userData || !userData.id) {
          alert('User data not found. Please log in again.');
          return;
        }
        
        const formData = new FormData(e.target);
        
        try {
          const queryParams = new URLSearchParams({
            id: userData.id,
            name: userData.name || '',
            contact: userData.email || userData.contact || ''
          }).toString();
          
          const endpoint = `${API_CONFIG.ENDPOINTS.SEARCH_CLIENT}?${queryParams}`;
          const currentClientData = await callGetApi(endpoint, null, token);
          

          const contactInputs = e.target.querySelectorAll('input[name="contactEmail"]');
          const contacts = [];
          contactInputs.forEach(input => {
            if (input.value) {
              contacts.push(input.value.trim());
            }
          });

          if (contacts.length === 0) {
            alert('Please add at least one contact email');
            return;
          }

          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(contacts[0])) {
            alert('Primary contact must be a email');
            return;
          }
          
          const updateData = {
            ...currentClientData,
            name: formData.get('name'),
            contact: contacts,
            updatedAt: new Date().toISOString()
          };

          await callPutApi(API_CONFIG.ENDPOINTS.UPDATE_CLIENT, updateData, token);
          
          alert('Account updated successfully!');
          
          const updatedUserData = {
            ...userData,
            name: formData.get('name'),
          };
          localStorage.setItem('userData', JSON.stringify(updatedUserData));
          
          const dashboardHeader = document.querySelector('.dashboard-header h1');
          if (dashboardHeader) {
            dashboardHeader.textContent = `Welcome, ${formData.get('name') || 'User'}`;
          }
          
          fetchClientData(updatedUserData);
          
        } catch (error) {
          alert(`Error updating account: ${error.message}`);
          console.error('Error updating account:', error);
        }
      });
    }
  }

  function getPlanName(planNumber) {
    const plans = ['Standard', 'Plus', 'Premium'];
    return plans[planNumber] || 'Standard';
  }
  
  function generateUrlList(urls) {
    if (!urls.length) {
      return '<p>No URLs added yet.</p>';
    }
    
    return urls.map(url => `
      <div class="url-item" data-url='${JSON.stringify(url)}' style="cursor: pointer;">
        <div class="url-info">
          <span class="url-address">${url.url}</span>
          <span class="url-status ${url.isActive ? 'active' : 'inactive'}">
            ${url.isActive ? 'Active' : 'Inactive'}
          </span>
          ${url.checkSeo ? '<span class="url-seo">SEO</span>' : ''}
        </div>
        <div class="url-frequency">
          Checking: ${url.frequency}
        </div>
      </div>
    `).join('');
  }
  
  document.addEventListener('click', function(e) {
    const urlItem = e.target.closest('.url-item');
    if (urlItem && urlItem.dataset.url) {
      const urlData = JSON.parse(urlItem.dataset.url);
      showEditUrlPopup(urlData);
    }
  });

  function showEditUrlPopup(urlData) {
    let editUrlPopup = document.getElementById('editUrlPopup');
    
    if (!editUrlPopup) {
      editUrlPopup = document.createElement('div');
      editUrlPopup.id = 'editUrlPopup';
      editUrlPopup.className = 'popup-container';
      
      if (!document.getElementById('urlPopupStyles')) {
        const urlPopupStyles = document.createElement('style');
        urlPopupStyles.id = 'urlPopupStyles';
        urlPopupStyles.textContent = `
          .popup-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
          }
          
          .popup-content {
            background: #fff;
            border-radius: 12px;
            max-width: 500px;
            width: 95%;
            padding: 30px;
            position: relative;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
          }
          
          .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            transition: color 0.2s ease;
          }
          
          .close-button:hover {
            color: #000;
          }
          
          .popup-content h2 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.8rem;
            text-align: center;
          }
          
          .form-group {
            margin-bottom: 20px;
          }
          
          .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary);
          }
          
          .form-group input[type="url"],
          .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border 0.2s ease;
          }
          
          .form-group input[type="url"]:focus,
          .form-group select:focus {
            border-color: var(--yellow);
            outline: none;
            box-shadow: 0 0 0 2px rgba(239, 201, 91, 0.3);
          }
          
          .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
          }
          
          .checkbox-label input {
            margin-right: 8px;
            width: 18px;
            height: 18px;
          }
          
          .checkbox-label span {
            font-weight: 600;
            color: var(--primary);
          }
          
          .fetch-button {
            background: var(--yellow);
            color: var(--dark);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background 0.2s ease, transform 0.2s ease;
          }
          
          .fetch-button:hover {
            background: var(--yellow-dark);
            transform: translateY(-2px);
          }
          
          .fetch-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
          }
        `;
        document.head.appendChild(urlPopupStyles);
      }
      
      document.body.appendChild(editUrlPopup);
    }
    
    const userData = JSON.parse(localStorage.getItem('userData'));
    const clientPlan = userData?.plan || 0;
    
    editUrlPopup.innerHTML = `
      <div class="popup-content">
        <button id="closeEditUrlPopup" class="close-button">&times;</button>
        <h2>Edit URL</h2>
        <form id="editUrlForm">
          <input type="hidden" id="originalUrl" name="originalUrl" value="${urlData.url}">
          <div class="form-group">
            <label for="editUrl">URL:</label>
            <input type="url" id="editUrl" name="url" value="${urlData.url}" placeholder="https://example.com" required readonly>
            <small>URL cannot be changed</small>
          </div>
          <div class="form-group">
            <label for="editUrlFrequency">Check Frequency:</label>
            <select id="editUrlFrequency" name="frequency" required>
              <option value="Daily" ${urlData.frequency === 'Daily' ? 'selected' : ''}>Daily</option>
              <option value="Hourly" ${urlData.frequency === 'Hourly' ? 'selected' : ''}>Hourly</option>
              <option value="H2" ${urlData.frequency === 'H2' ? 'selected' : ''}>Every 2 Hours</option>
              <option value="H6" ${urlData.frequency === 'H6' ? 'selected' : ''}>Every 6 Hours</option>
              <option value="H12" ${urlData.frequency === 'H12' ? 'selected' : ''}>Every 12 Hours</option>
            </select>
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="editUrlActive" name="isActive" ${urlData.isActive ? 'checked' : ''}>
              <span>Active</span>
            </label>
          </div>
          <div class="form-group checkbox-group" ${clientPlan === 0 ? 'style="display:none;"' : ''}>
            <label class="checkbox-label">
              <input type="checkbox" id="editUrlSeo" name="checkSeo" ${urlData.checkSeo ? 'checked' : ''}>
              <span>Check SEO</span>
            </label>
          </div>
          <button type="submit" id="updateUrlButton" class="fetch-button" disabled>Update URL</button>
        </form>
      </div>
    `;
    
    editUrlPopup.style.display = 'flex';
    
    document.getElementById('closeEditUrlPopup').addEventListener('click', () => {
      editUrlPopup.style.display = 'none';
    });
    
    editUrlPopup.addEventListener('click', (e) => {
      if (e.target === editUrlPopup) {
        editUrlPopup.style.display = 'none';
      }
    });
    
    const form = document.getElementById('editUrlForm');
    const updateButton = document.getElementById('updateUrlButton');
    const originalFrequency = urlData.frequency;
    const originalActive = urlData.isActive;
    const originalSeo = urlData.checkSeo;
    
    const checkForChanges = () => {
      const currentFrequency = form.elements.frequency.value;
      const currentActive = form.elements.isActive.checked;
      const currentSeo = clientPlan === 0 ? false : (form.elements.checkSeo?.checked ?? originalSeo);
      
      if (currentFrequency !== originalFrequency || 
          currentActive !== originalActive || 
          currentSeo !== originalSeo) {
        updateButton.disabled = false;
      } else {
        updateButton.disabled = true;
      }
    };
    
    form.elements.frequency.addEventListener('change', checkForChanges);
    form.elements.isActive.addEventListener('change', checkForChanges);
    if (clientPlan !== 0 && form.elements.checkSeo) {
      form.elements.checkSeo.addEventListener('change', checkForChanges);
    }
    
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const token = localStorage.getItem('userToken');
      if (!token) {
        alert('You must be logged in to update URLs.');
        return;
      }
      
      const userData = JSON.parse(localStorage.getItem('userData'));
      if (!userData || !userData.id) {
        alert('User data not found. Please log in again.');
        return;
      }
      
      const formData = new FormData(e.target);
      const originalUrl = formData.get('originalUrl');
      
      try {
        const queryParams = new URLSearchParams({
          id: userData.id,
          name: userData.name || '',
          contact: userData.email || userData.contact || ''
        }).toString();
        
        const endpoint = `${API_CONFIG.ENDPOINTS.SEARCH_CLIENT}?${queryParams}`;
        const currentClientData = await callGetApi(endpoint, null, token);
        
        const updatedUrls = currentClientData.urls.map(url => {
          if (url.url === originalUrl) {
            return {
              url: originalUrl,
              frequency: formData.get('frequency'),
              isActive: formData.get('isActive') === 'on',
              checkSeo: clientPlan === 0 ? false : (formData.get('checkSeo') === 'on')
            };
          }
          return url;
        });
        
        const updateData = {
          ...currentClientData,
          urls: updatedUrls,
          updatedAt: new Date().toISOString()
        };
        
        await callPutApi(API_CONFIG.ENDPOINTS.UPDATE_CLIENT, updateData, token);
        
        alert('URL updated successfully!');
        editUrlPopup.style.display = 'none';
        
        if (typeof window.fetchClientData === 'function') {
          window.fetchClientData(userData);
        } else {
          location.reload();
        }
        
      } catch (error) {
        alert(`Error updating URL: ${error.message}`);
        console.error('Error updating URL:', error);
      }
    });
  }

  const searchButtonStyle = document.createElement('style');
  searchButtonStyle.textContent = `
    .date-filters {
      display: flex;
      align-items: center;
      gap: 15px;
      flex-wrap: wrap;
    }
    
    .search-button {
      margin-left: 10px;
      margin-top: 10px;
    }
    
    @media (max-width: 768px) {
      .date-filters {
        flex-direction: column;
        align-items: flex-start;
      }
      
      .search-button {
        margin-top: 15px;
        width: 100%;
      }
    }
  `;
  document.head.appendChild(searchButtonStyle);

  const storedToken = localStorage.getItem('userToken');
  const storedUserData = localStorage.getItem('userData');
  
  if (storedToken && storedUserData) {
    try {
      const userData = JSON.parse(storedUserData);
      showDashboard(userData);
    } catch (e) {
      console.error('Error parsing stored user data', e);
      localStorage.removeItem('userToken');
      localStorage.removeItem('userData');
    }
  }

  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('showLogin') === 'true') {
    
    const loginPopup = document.getElementById('loginPopup');
    if (loginPopup) {
      loginPopup.style.display = 'flex';
    }
    
    const newUrl = window.location.pathname + window.location.hash;
    window.history.replaceState({}, document.title, newUrl);

  }  else if (urlParams.get('newCreditBuy') === 'true') {

    if (typeof storedUserData !== 'undefined') {
      showAddCreditsPopup(storedUserData);
    } 

    const newUrl = window.location.pathname + window.location.hash;
    window.history.replaceState({}, document.title, newUrl);
  }

});

function formatNumberWithPeriods(number) {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

function isTokenValid(token) {
  if (!token) return false;
  
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const payload = JSON.parse(jsonPayload);
    
    if (payload.exp) {
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      
      return currentTime < expirationTime;
    }
    
    return true;
  } catch (e) {
    console.error('Error checking token validity', e);
    return false;
  }
}

function handleAuthError(error) {
  if (error.message && (
      error.message.includes('token') || 
      error.message.includes('auth') || 
      error.message.includes('unauthorized') ||
      error.message.includes('forbidden')
    )) {
    console.warn('Authentication error detected, clearing token');
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    
    const dashboardContainer = document.querySelector('.dashboard-container');
    if (dashboardContainer) {
      document.body.removeChild(dashboardContainer);
      
      document.querySelectorAll('section, header, footer, .top-bar').forEach(el => {
        el.style.display = '';
      });
      
      const loginPopup = document.getElementById('loginPopup');
      if (loginPopup) {
        loginPopup.style.display = 'flex';
        alert('Your session has expired. Please log in again.');
      }
    }
  }
}

document.addEventListener('DOMContentLoaded', () => {
  const storedToken = localStorage.getItem('userToken');
  
  if (storedToken && !isTokenValid(storedToken)) {
    console.warn('Stored token is invalid or expired, clearing');
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
  }
});

function showAddCreditsPopup(clientData) {
  if (!document.getElementById('creditsPopupStyles')) {
    const creditsPopupStyles = document.createElement('style');
    creditsPopupStyles.id = 'creditsPopupStyles';
    creditsPopupStyles.textContent = `
      .credits-option {
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .credits-option:hover {
        border-color: var(--yellow);
        transform: translateY(-2px);
      }
      
      .credits-option.selected {
        border-color: var(--yellow);
        background-color: rgba(239, 201, 91, 0.1);
      }
      
      .credits-option h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: var(--primary);
        font-size: 1.3rem;
      }
      
      .credits-option p {
        margin: 8px 0;
        font-size: 1rem;
      }
      
      #creditsOptions {
        max-height: 450px;
        overflow-y: auto;
      }
      
      @media (max-width: 768px) {
        .credits-option h3 {
          font-size: 1.2rem;
        }
        
        .credits-option p {
          font-size: 0.95rem;
        }
      }
    `;
    document.head.appendChild(creditsPopupStyles);
  }
  

  const addCreditsPopup = document.createElement('div');
  addCreditsPopup.id = 'addCreditsPopup';
  addCreditsPopup.className = 'popup-container';
  addCreditsPopup.style.display = 'flex';
  
  addCreditsPopup.innerHTML = `
    <div class="popup-content" style="max-width: 500px; width: 90%;">
      <button id="closeCreditsPopup" class="close-button">&times;</button>
      <h2 style="margin-bottom: 15px; font-size: 1.8rem;">Add Credits</h2>
      <p style="margin-bottom: 20px; color: #666; font-style: italic;">All discounts are already applied to the displayed prices.</p>
      
      <div id="creditsOptions">
        <div class="credits-option" data-credits="1000" data-price="6.56" data-pack="Small">
          <h3>🎯 Small Pack</h3>
          <p><strong>1,000 Credits - $6.56</strong></p>
          <p>No discount</p>
        </div>
        
        <div class="credits-option" data-credits="5000" data-price="30.00" data-pack="Medium">
          <h3>⚡ Medium Pack</h3>
          <p><strong>5,000 Credits - $30.00</strong></p>
          <p>~9% off</p>
        </div>
        
        <div class="credits-option" data-credits="10000" data-price="56.00" data-pack="Large">
          <h3>🚀 Large Pack</h3>
          <p><strong>10,000 Credits - $56.00</strong></p>
          <p>~15% off</p>
        </div>
        
        <div class="credits-option" data-credits="50000" data-price="260.00" data-pack="Enterprise">
          <h3>🏢 Enterprise Pack</h3>
          <p><strong>50,000 Credits - $260.00</strong></p>
          <p>~20% off</p>
        </div>
        
        <div class="credits-option" data-credits="100000" data-price="500.00" data-pack="Corporate">
          <h3>🏛 Corporate Pack</h3>
          <p><strong>100,000 Credits - $500.00</strong></p>
          <p>~24% off</p>
        </div>
      </div>
      
      <div style="margin-top:25px;">
        <p style="margin-bottom:15px; font-size: 1.1rem;">Selected: <span id="selectedCreditsText">None</span></p>
        <button id="purchaseCreditsBtn" class="fetch-button" style="padding: 15px; font-size: 1.1rem;" disabled>Purchase Credits</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(addCreditsPopup);
  

  document.getElementById('closeCreditsPopup').addEventListener('click', () => {
    document.body.removeChild(addCreditsPopup);
  });
  
  addCreditsPopup.addEventListener('click', (e) => {
    if (e.target === addCreditsPopup) {
      document.body.removeChild(addCreditsPopup);
    }
  });
  
  let selectedOption = null;
  

  const creditOptions = document.querySelectorAll('.credits-option');
  creditOptions.forEach(option => {
    option.addEventListener('click', () => {
      creditOptions.forEach(opt => opt.classList.remove('selected'));

      option.classList.add('selected');
      const credits = option.getAttribute('data-credits');
      const price = option.getAttribute('data-price');
      document.getElementById('selectedCreditsText').textContent = 
        `${credits} Credits for $${price}`;
      
      // Enable purchase button
      const purchaseBtn = document.getElementById('purchaseCreditsBtn');
      purchaseBtn.disabled = false;
      
      // Store selected option
      selectedOption = {
        credits: parseInt(credits),
        price: parseFloat(price),
        pack: option.getAttribute('data-pack')
      };
    });
  });
  
  async function purchaseCredits(clientData, packType) {
    const token = localStorage.getItem('userToken');
    if (!token) {
      throw new Error('You must be logged in to purchase credits');
    }
    
    const packMap = {
      'Small': 0,
      'Medium': 1,
      'Large': 2,
      'Enterprise': 3,
      'Corporate': 4
    };
    
    const addCreditsData = {
      creditsDto: {
        Client: clientData,  // Note: case sensitivity matters (Client vs client)
        Pack: packMap[packType] || 0  // Convert pack name to enum integer
      }
    };
    
    return await callApi(API_CONFIG.ENDPOINTS.ADD_CREDITS, addCreditsData, token);
  }

  document.getElementById('purchaseCreditsBtn').addEventListener('click', async () => {
    if (!selectedOption) return;
    
    const purchaseBtn = document.getElementById('purchaseCreditsBtn');
    const originalBtnText = purchaseBtn.textContent;
    purchaseBtn.textContent = 'Processing...';
    purchaseBtn.disabled = true;
    
    try {
      const response = await purchaseCredits(clientData, selectedOption.pack);

      // Get the popup content element
      const popupContent = addCreditsPopup.querySelector('.popup-content');
      
      // Clear existing content
      while (popupContent.firstChild) {
        popupContent.removeChild(popupContent.firstChild);
      }
      
      // Create close button
      const closeButton = document.createElement('button');
      closeButton.id = 'closeCreditsPopup';
      closeButton.className = 'close-button';
      closeButton.innerHTML = '&times;';
      closeButton.addEventListener('click', () => {
        document.body.removeChild(addCreditsPopup);
      });
      
      // Create new elements
      const title = document.createElement('h2');
      title.style.marginBottom = '20px';
      title.style.fontSize = '1.8rem';
      title.textContent = 'Order Created';
      
      const message = document.createElement('p');
      message.style.marginBottom = '25px';
      message.style.fontSize = '1.1rem';
      message.textContent = 'Your new credits order is successfully created, please click the button below to proceed to the payment.';
      
      const paymentBtn = document.createElement('button');
      paymentBtn.id = 'proceedToPaymentBtn';
      paymentBtn.className = 'fetch-button';
      paymentBtn.style.padding = '15px';
      paymentBtn.style.fontSize = '1.1rem';
      paymentBtn.style.marginBottom = '15px';
      paymentBtn.textContent = 'Proceed to Payment';
      
      const note = document.createElement('p');
      note.style.color = '#666';
      note.style.fontStyle = 'italic';
      note.textContent = 'Note that if you can\'t pay right now, we already sent the link to your registered contacts.';
      
      // Append new elements
      popupContent.appendChild(closeButton);
      popupContent.appendChild(title);
      popupContent.appendChild(message);
      popupContent.appendChild(paymentBtn);
      popupContent.appendChild(note);
      
      // Add event listener to payment button
      paymentBtn.addEventListener('click', () => {
        if (response && response.paymentUrl) {
          window.open(response.paymentUrl, '_blank');
        } else {
          window.open(response, '_blank');
        }
      });
      
    } catch (error) {
      alert(`Error initiating payment: ${error.message}`);
      console.error('Error initiating payment:', error);
      
      // Reset button
      purchaseBtn.textContent = originalBtnText;
      purchaseBtn.disabled = false;
    }
  });
}
