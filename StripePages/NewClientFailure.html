<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Registration Failed | Sasquat</title>
  <meta property="og:description" content="Oops! Something went wrong with your registration. Please try again to get your websites monitored.">
  <link rel="icon" href="../assets/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="../styles.css">
  <style>
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .content {
      flex: 1;
    }
    
    footer {
      margin-top: auto;
    }
  </style>
</head>
<body>
  <div class="content">
    <section class="section" style="background:#fff7d6; border:2px solid #e53935; border-radius:12px; padding:30px; max-width:800px; margin:40px auto; text-align:center;">
      <img src="../assets/sasquat_sad.png" alt="Sasquat Mascot" style="width:100px; margin-bottom:20px;">
      <h2 style="color:#e53935;">⚠️ Oops! Registration Failed</h2>
      <p style="font-size:1.1rem; color:#333; margin:15px 0 10px;">Looks like something went wrong while processing your registration.</p>
      <p style="font-size:1rem; color:#555;">But, please, before you try to register again, check if you already have an account with us by logging in.</p>
      <a href="../index.html" class="cta-button" style="display:inline-block; margin-top:20px;">Try to Login</a>
    </section>
  </div>

  <footer>
    <p>&copy; 2025 Sasquat Inc. All rights reserved. | Built with monster love 💛</p>
  </footer>

  <script>
    function goToLoginPage() {
      window.location.href = "../index.html?showLogin=true";
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      const loginButton = document.querySelector('.cta-button');
      if (loginButton) {
        loginButton.addEventListener('click', function(e) {
          e.preventDefault();
          goToLoginPage();
        });
      }
    });
  </script>

</body>
</html>