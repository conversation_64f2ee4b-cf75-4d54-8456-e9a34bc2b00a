<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Registration Successful | Sasquat</title>
  <meta name="description" content="Your account has been created successfully. Sasquat is now watching your websites 24/7." />
  <link rel="icon" href="../assets/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="../styles.css">
  <style>
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .content {
      flex: 1;
    }
    
    footer {
      margin-top: auto;
    }
  </style>
</head>
<body>
  <div class="content">
    <section class="section" style="background:#fff7d6; border:2px solid #35e53e; border-radius:12px; padding:30px; max-width:800px; margin:40px auto; text-align:center;">
      <img src="../assets/sasquat_mascot.png" alt="Sasquat Mascot" style="width:120px; margin-bottom:20px;">
      <h2 style="color:#1a3f5b;">✅ Registration Successful!</h2>
      <p style="font-size:1.2rem; max-width:600px; margin:20px auto;">Your account has been created successfully. Sasquat is now on patrol keeping an eye on your websites. 🐾</p>
      <p style="font-size:1.1rem; max-width:600px; margin:20px auto;">We've sent a confirmation email with your login details. You can now access your dashboard to monitor your websites.</p>
      <a class="cta-button" style="display:inline-block; margin-top:20px;">Log In Now</a>
    </section>
  </div>

  <footer>
    <p>&copy; 2025 Sasquat Inc. All rights reserved. | Stay wild, stay online 🦶</p>
  </footer>

  <script>
    function goToLoginPage() {
      window.location.href = "../index.html?showLogin=true";
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      const loginButton = document.querySelector('.cta-button');
      if (loginButton) {
        loginButton.addEventListener('click', function(e) {
          e.preventDefault();
          goToLoginPage();
        });
      }
    });
  </script>
</body>
</html>
